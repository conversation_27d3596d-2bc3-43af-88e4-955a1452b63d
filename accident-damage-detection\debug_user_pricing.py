#!/usr/bin/env python3
"""
Debug script to check user-specific pricing issues
"""

import sqlite3
import os
from app import get_part_prices, normalize_brand_name, normalize_model_name

def debug_user_pricing():
    """Debug pricing for all users in the database"""
    
    print("🔍 DEBUGGING USER-SPECIFIC PRICING ISSUES")
    print("=" * 60)
    
    # Connect to database
    db_path = 'vehicle_damage.db'
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Get all users
    cursor.execute("SELECT email, car_brand, model FROM user_info")
    users = cursor.fetchall()
    
    print(f"📋 Found {len(users)} users in database:")
    print("-" * 60)
    
    for user in users:
        email = user['email']
        car_brand = user['car_brand']
        model = user['model']
        
        print(f"\n👤 User: {email}")
        print(f"🚗 Vehicle: {car_brand} {model}")
        
        # Normalize brand and model
        normalized_brand = normalize_brand_name(car_brand)
        normalized_model = normalize_model_name(model)
        print(f"🔄 Normalized: {normalized_brand} {normalized_model}")
        
        # Check if pricing data exists for this car
        cursor.execute("""
            SELECT part, price FROM car_models 
            WHERE UPPER(brand) = UPPER(?) AND UPPER(model) = UPPER(?)
        """, (normalized_brand, normalized_model))
        
        pricing_data = cursor.fetchall()
        
        if pricing_data:
            print(f"✅ Pricing data found: {len(pricing_data)} parts")
            for part_data in pricing_data:
                print(f"   {part_data['part']}: ₹{part_data['price']:,}")
            
            # Test with sample damage
            sample_class_counts = {3: 1, 1: 1}  # Door and Bumper
            part_prices = get_part_prices(email, sample_class_counts)
            
            if part_prices:
                total = sum(item['total'] for item in part_prices.values())
                print(f"✅ Sample pricing test: ₹{total:,}")
            else:
                print("❌ Sample pricing test failed")
        else:
            print("❌ No pricing data found for this vehicle")
            
            # Check for similar entries
            cursor.execute("""
                SELECT DISTINCT brand, model FROM car_models 
                WHERE UPPER(brand) LIKE '%' || UPPER(?) || '%'
            """, (car_brand,))
            
            similar_brands = cursor.fetchall()
            if similar_brands:
                print("🔍 Similar brands found:")
                for similar in similar_brands:
                    print(f"   {similar['brand']} {similar['model']}")
            else:
                print("❌ No similar brands found in database")
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    print("If a user shows 'No pricing data found', they will get the pricing error.")
    print("Solutions:")
    print("1. Update user's car_brand/model to match database entries")
    print("2. Add pricing data for their specific vehicle")
    print("3. Use the demo user (<EMAIL>) which is guaranteed to work")
    
    conn.close()

def show_available_vehicles():
    """Show all available vehicles in the pricing database"""
    
    print("\n🚗 AVAILABLE VEHICLES IN PRICING DATABASE:")
    print("=" * 60)
    
    db_path = 'vehicle_damage.db'
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    cursor.execute("SELECT DISTINCT brand, model FROM car_models ORDER BY brand, model")
    vehicles = cursor.fetchall()
    
    current_brand = None
    for vehicle in vehicles:
        if vehicle['brand'] != current_brand:
            current_brand = vehicle['brand']
            print(f"\n🏭 {current_brand}:")
        print(f"   • {vehicle['model']}")
    
    conn.close()

if __name__ == "__main__":
    debug_user_pricing()
    show_available_vehicles()
