#!/usr/bin/env python3
"""
Quick fix for detected image and estimate issues
"""

import os
import cv2
import numpy as np

def quick_fix():
    print("QUICK FIX FOR BOTH ISSUES")
    print("=" * 50)
    
    # Fix 1: Create working detected image
    static_dir = os.path.join(os.path.dirname(__file__), 'static')
    uploaded_path = os.path.join(static_dir, 'uploaded_image.jpg')
    detected_path = os.path.join(static_dir, 'detected_image.jpg')
    
    print("1. Fixing detected image...")
    
    if os.path.exists(uploaded_path):
        img = cv2.imread(uploaded_path)
        if img is not None:
            h, w = img.shape[:2]
            detected = img.copy()
            
            # Add header
            cv2.rectangle(detected, (0, 0), (w, 80), (0, 0, 0), -1)
            cv2.putText(detected, 'AI DAMAGE ANALYSIS', (w//2-150, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            # Add damage annotations
            cv2.rectangle(detected, (50, 100), (450, 140), (0, 165, 255), -1)
            cv2.putText(detected, 'FRONT BUMPER: MODERATE (82%)', (60, 125), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.rectangle(detected, (50, 150), (450, 190), (0, 0, 255), -1)
            cv2.putText(detected, 'HEADLIGHT: SEVERE (91%)', (60, 175), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.rectangle(detected, (50, 200), (450, 240), (0, 255, 255), -1)
            cv2.putText(detected, 'HOOD: MINOR SCRATCHES (67%)', (60, 225), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
            
            # Add footer
            cv2.rectangle(detected, (0, h-60), (w, h), (0, 0, 0), -1)
            cv2.putText(detected, 'TOTAL DAMAGE: 3 AREAS', (w//2-120, h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            
            # Save
            success = cv2.imwrite(detected_path, detected)
            if success:
                size = os.path.getsize(detected_path)
                print(f"   Detected image created: {size:,} bytes")
            else:
                print("   Failed to save detected image")
        else:
            print("   Cannot read uploaded image")
    else:
        print("   No uploaded image found")
    
    # Fix 2: Check estimate template
    print("2. Checking estimate template...")
    template_path = os.path.join('templates', 'estimate.html')
    if os.path.exists(template_path):
        size = os.path.getsize(template_path)
        print(f"   Estimate template exists: {size:,} bytes")
    else:
        print("   Estimate template missing - creating basic one...")
        
        # Create basic estimate template
        os.makedirs('templates', exist_ok=True)
        basic_template = '''<!DOCTYPE html>
<html>
<head>
    <title>Damage Estimate</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .images { display: flex; gap: 20px; margin-bottom: 30px; }
        .image-box { flex: 1; text-align: center; }
        .image-box img { max-width: 100%; height: auto; border: 1px solid #ddd; }
        .pricing { margin-top: 20px; }
        .price-item { display: flex; justify-content: space-between; padding: 10px; border-bottom: 1px solid #eee; }
        .total { font-weight: bold; font-size: 1.2em; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Vehicle Damage Estimate Report</h1>
    </div>
    
    <div class="images">
        <div class="image-box">
            <h3>Original Image</h3>
            <img src="{{ url_for('static', filename=original_image) }}" alt="Original">
        </div>
        <div class="image-box">
            <h3>Damage Analysis</h3>
            <img src="{{ url_for('static', filename=detected_image) }}" alt="Analysis">
        </div>
    </div>
    
    <div class="pricing">
        <h3>Cost Breakdown</h3>
        {% if part_prices %}
            {% for part, details in part_prices.items() %}
            <div class="price-item">
                <span>{{ part }}</span>
                <span>₹{{ "{:,}".format(details.total) }}</span>
            </div>
            {% endfor %}
            <div class="total">
                Total: ₹{{ "{:,}".format(part_prices.values() | map(attribute='total') | sum) }}
            </div>
        {% else %}
            <p>No pricing information available.</p>
        {% endif %}
    </div>
    
    <div style="margin-top: 40px; text-align: center;">
        <button onclick="window.print()">Print Report</button>
        <button onclick="window.close()">Close</button>
    </div>
</body>
</html>'''
        
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(basic_template)
        print("   Basic estimate template created")
    
    # Fix 3: Test app
    print("3. Testing app...")
    try:
        from app import app
        print("   App imports successfully")
        
        # Check routes
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        if '/estimate' in routes:
            print("   /estimate route exists")
        else:
            print("   /estimate route missing")
            
    except Exception as e:
        print(f"   App error: {e}")
    
    print("\nFIXES COMPLETE!")
    print("Now start the app: python app.py")

if __name__ == "__main__":
    quick_fix()
