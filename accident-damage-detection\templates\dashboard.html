{% extends "base.html" %}

{% block title %}Dashboard - VehicleCare Pro{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 2rem;
        border-radius: var(--border-radius);
        margin-bottom: 2rem;
        text-align: center;
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .dashboard-subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .upload-section {
        background: var(--bg-primary);
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .upload-area {
        border: 2px dashed var(--border-color);
        border-radius: var(--border-radius);
        padding: 3rem 2rem;
        text-align: center;
        transition: all 0.3s ease;
        background: var(--bg-tertiary);
        margin: 1rem 0;
        cursor: pointer;
    }

    .upload-area:hover {
        border-color: var(--primary-color);
        background: var(--bg-secondary);
    }

    .upload-area.dragover {
        border-color: var(--primary-color);
        background: rgb(37 99 235 / 0.05);
    }

    .upload-icon {
        font-size: 3rem;
        color: var(--text-muted);
        margin-bottom: 1rem;
    }

    .upload-text {
        color: var(--text-secondary);
        margin-bottom: 1rem;
    }

    .upload-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .upload-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .file-input {
        display: none;
    }

    .upload-options {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        margin: 1rem 0;
        flex-wrap: wrap;
    }

    .option-btn {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius);
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
    }

    .option-btn:hover {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
        transform: translateY(-1px);
    }

    .option-btn i {
        font-size: 0.875rem;
    }

    .quick-stats {
        background: var(--bg-primary);
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .stat-item {
        text-align: center;
        padding: 1rem;
        background: var(--bg-tertiary);
        border-radius: var(--border-radius);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.25rem;
    }

    .stat-label {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .results-section {
        grid-column: 1 / -1;
        background: var(--bg-primary);
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        display: none;
    }

    .results-section.show {
        display: block;
    }

    .image-comparison {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin: 2rem 0;
    }

    .image-box {
        text-align: center;
    }

    .image-box h3 {
        margin-bottom: 1rem;
        color: var(--text-primary);
        font-weight: 600;
    }

    .image-box img {
        max-width: 100%;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .damage-analysis {
        background: var(--bg-tertiary);
        padding: 1.5rem;
        border-radius: var(--border-radius);
        margin-top: 2rem;
    }

    .damage-analysis h3 {
        margin: 0 0 1rem 0;
        color: var(--text-primary);
        font-weight: 600;
    }

    .damage-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .damage-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--border-color);
    }

    .damage-item:last-child {
        border-bottom: none;
    }

    .damage-name {
        font-weight: 500;
        color: var(--text-primary);
    }

    .damage-count {
        background: var(--error-color);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .price-estimate {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        padding: 2rem;
        border-radius: var(--border-radius);
        text-align: center;
        margin-top: 2rem;
    }

    .price-estimate h3 {
        margin: 0 0 1rem 0;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .price-breakdown {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .price-item {
        text-align: center;
    }

    .price-item-label {
        font-size: 0.875rem;
        opacity: 0.9;
        margin-bottom: 0.25rem;
    }

    .price-item-value {
        font-size: 1.25rem;
        font-weight: 600;
    }

    .total-price {
        font-size: 2.5rem;
        font-weight: 700;
        margin-top: 1rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn-secondary {
        background: var(--bg-primary);
        color: var(--text-primary);
        border: 2px solid var(--border-color);
    }

    .btn-secondary:hover {
        background: var(--bg-tertiary);
        border-color: var(--primary-color);
    }

    @media print {
        /* Hide everything except results section when printing */
        .header, .dashboard-header, .dashboard-grid, .action-buttons {
            display: none !important;
        }

        .results-section {
            display: block !important;
            margin: 0;
            padding: 0;
            box-shadow: none;
            border: none;
        }

        .section-title {
            display: none;
        }

        .damage-analysis, .price-estimate {
            break-inside: avoid;
        }

        body {
            background: white !important;
        }
    }

    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }

        .image-comparison {
            grid-template-columns: 1fr;
        }

        .price-breakdown {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // File upload functionality
    const uploadArea = document.querySelector('.upload-area');
    const fileInput = document.getElementById('file-input');
    const uploadBtn = document.querySelector('.upload-btn');
    const uploadText = document.querySelector('.upload-text');

    // Click to upload
    uploadArea.addEventListener('click', () => {
        openFiles();
    });

    // Upload option functions
    function openCamera() {
        // Create a new file input for camera
        const cameraInput = document.createElement('input');
        cameraInput.type = 'file';
        cameraInput.accept = 'image/*';
        cameraInput.capture = 'environment'; // Use rear camera
        cameraInput.style.display = 'none';

        cameraInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                fileInput.files = e.target.files;
                updateUploadText(e.target.files[0]);
            }
        });

        document.body.appendChild(cameraInput);
        cameraInput.click();
        document.body.removeChild(cameraInput);
    }

    function openGallery() {
        // Create a new file input for gallery
        const galleryInput = document.createElement('input');
        galleryInput.type = 'file';
        galleryInput.accept = 'image/*';
        galleryInput.style.display = 'none';

        galleryInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                fileInput.files = e.target.files;
                updateUploadText(e.target.files[0]);
            }
        });

        document.body.appendChild(galleryInput);
        galleryInput.click();
        document.body.removeChild(galleryInput);
    }

    function openFiles() {
        fileInput.click();
    }

    // Drag and drop functionality
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
        uploadArea.style.borderColor = '#10b981';
        uploadArea.style.background = 'rgba(16, 185, 129, 0.1)';
    });

    uploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        uploadArea.style.borderColor = '';
        uploadArea.style.background = '';
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        uploadArea.style.borderColor = '';
        uploadArea.style.background = '';

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            // Validate that it's an image
            const file = files[0];
            if (file.type.startsWith('image/')) {
                fileInput.files = files;
                updateUploadText(file);
            } else {
                alert('Please drop an image file only.');
            }
        }
    });

    // File input change
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            updateUploadText(e.target.files[0]);
        }
    });

    function updateUploadText(file) {
        // Validate file type
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!validTypes.includes(file.type)) {
            alert('Please select a valid image file (JPG, PNG, GIF, WebP)');
            return;
        }

        // Validate file size (max 10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            alert('File size too large. Please select an image under 10MB.');
            return;
        }

        uploadText.innerHTML = `
            <strong>✅ ${file.name}</strong><br>
            <small>File size: ${(file.size / 1024 / 1024).toFixed(2)}MB • Ready to analyze</small>
        `;

        uploadBtn.style.background = 'linear-gradient(135deg, #10b981, #059669)';
        uploadBtn.innerHTML = '<i class="fas fa-search"></i> Analyze Vehicle Damage';
        uploadBtn.disabled = false;

        // Change upload area appearance
        uploadArea.style.borderColor = '#10b981';
        uploadArea.style.background = 'rgba(16, 185, 129, 0.05)';
    }

    // File input change
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            updateUploadText(e.target.files[0]);
        }
    });

    // Form submission
    document.getElementById('upload-form').addEventListener('submit', (e) => {
        if (!fileInput.files.length) {
            e.preventDefault();
            alert('Please select a vehicle image first.');
            return;
        }

        uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing Vehicle...';
        uploadBtn.disabled = true;

        // Show progress
        uploadText.innerHTML = `
            <strong>🔄 Processing your image...</strong><br>
            <small>AI is analyzing vehicle damage • Please wait</small>
        `;
    });

    // Print report function
    function printReport() {
        // Open estimate page in new window and print
        const printWindow = window.open('{{ url_for("estimate") }}', '_blank');
        printWindow.onload = function() {
            setTimeout(function() {
                printWindow.print();
            }, 500);
        };
    }
</script>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1 class="dashboard-title">Welcome to Your Dashboard</h1>
    <p class="dashboard-subtitle">Upload vehicle images to get instant AI-powered damage analysis and cost estimates</p>
</div>

<div class="dashboard-grid">
    <!-- Upload Section -->
    <div class="upload-section">
        <h2 class="section-title">
            <i class="fas fa-cloud-upload-alt"></i>
            Upload Vehicle Image
        </h2>
        
        <form action="{{ url_for('dashboard') }}" method="POST" enctype="multipart/form-data" id="upload-form">
            <div class="upload-area" id="upload-area">
                <div class="upload-icon">
                    <i class="fas fa-car"></i>
                </div>
                <div class="upload-text" id="upload-text">
                    <strong>Click to upload vehicle image</strong><br>
                    <small>From camera, gallery, or computer • JPG, PNG, JPEG</small>
                </div>
                <input type="file" id="file-input" name="file" accept="image/*" class="file-input" capture="environment" required>
            </div>

            <div class="upload-options">
                <button type="button" class="option-btn" onclick="openCamera()">
                    <i class="fas fa-camera"></i>
                    Take Photo
                </button>
                <button type="button" class="option-btn" onclick="openGallery()">
                    <i class="fas fa-images"></i>
                    Choose from Gallery
                </button>
                <button type="button" class="option-btn" onclick="openFiles()">
                    <i class="fas fa-folder-open"></i>
                    Browse Files
                </button>
            </div>

            <button type="submit" class="upload-btn" id="upload-btn" disabled>
                <i class="fas fa-search"></i>
                Analyze Vehicle Damage
            </button>
        </form>
    </div>

    <!-- Quick Stats -->
    <div class="quick-stats">
        <h2 class="section-title">
            <i class="fas fa-chart-bar"></i>
            Quick Stats
        </h2>
        
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">99.2%</div>
                <div class="stat-label">Accuracy</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">&lt; 15s</div>
                <div class="stat-label">Processing Time</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">Availability</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">50K+</div>
                <div class="stat-label">Vehicles Analyzed</div>
            </div>
        </div>
    </div>
</div>

<!-- Results Section (Hidden by default) -->
{% if uploaded_image_path and detected_image_path %}
<div class="results-section show">
    <h2 class="section-title">
        <i class="fas fa-search"></i>
        Analysis Results
    </h2>
    
    <div class="image-comparison">
        <div class="image-box">
            <h3>Original Image</h3>
            <img src="{{ url_for('static', filename='uploaded_image.jpg') }}" alt="Original Image">
        </div>
        <div class="image-box">
            <h3>Detected Damage</h3>
            <img src="{{ url_for('static', filename='detected_image.jpg') }}" alt="Detected Damage">
        </div>
    </div>
    
    {% if damage_info %}
    <div class="damage-analysis">
        <h3><i class="fas fa-exclamation-triangle"></i> Detected Damage</h3>
        <ul class="damage-list">
            {% for damage_type, count in damage_info.items() %}
            <li class="damage-item">
                <span class="damage-name">{{ damage_type }}</span>
                <span class="damage-count">{{ count }}</span>
            </li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
    
    {% if prices %}
    <div class="price-estimate">
        <h3><i class="fas fa-calculator"></i> Repair Cost Estimate</h3>
        
        <div class="price-breakdown">
            {% for part, details in prices.items() %}
            <div class="price-item">
                <div class="price-item-label">{{ part }}</div>
                <div class="price-item-value">₹{{ details.total }}</div>
            </div>
            {% endfor %}
        </div>
        
        <div class="total-price">
            Total: ₹{{ total_cost }}
        </div>
        
        <div class="action-buttons">
            <a href="{{ url_for('estimate') }}" class="btn btn-secondary">
                <i class="fas fa-file-alt"></i>
                View Full Report
            </a>
            <a href="{{ url_for('view_profile') }}" class="btn btn-secondary">
                <i class="fas fa-user"></i>
                View Profile
            </a>
            <button onclick="printReport()" class="btn btn-primary">
                <i class="fas fa-print"></i>
                Print Report
            </button>
        </div>
    </div>
    {% else %}
    <!-- Show message when no prices available -->
    {% if damage_info %}
    <div class="price-estimate" style="background: #f59e0b;">
        <h3><i class="fas fa-exclamation-triangle"></i> Pricing Information</h3>
        <p>Damage detected but pricing information is not available.</p>
        <p>Please contact support for manual cost estimation.</p>
    </div>
    {% endif %}
    {% endif %}
</div>
{% endif %}
{% endblock %}
