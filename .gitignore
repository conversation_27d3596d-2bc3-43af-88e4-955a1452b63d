# VehicleCare Pro - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# VehicleCare Pro Specific Files

# Database files (keep structure, ignore data)
accident-damage-detection/vehicle_damage.db
accident-damage-detection/vehicle_damage.db-journal

# Uploaded images (temporary files)
accident-damage-detection/static/uploaded_image.jpg
accident-damage-detection/static/detected_image.jpg
accident-damage-detection/static/temp_*
accident-damage-detection/static/uploads/

# AI Model files (large files)
accident-damage-detection/models/best.pt
accident-damage-detection/models/*.pt
accident-damage-detection/models/*.onnx
accident-damage-detection/models/*.weights

# Test files
accident-damage-detection/test_*.py
test_*.py

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
accident-damage-detection/logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Configuration files with sensitive data
config.py
.env.local
.env.production

# Backup files
*.bak
*.backup

# Documentation build
docs/build/
docs/_build/

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Virtual environments
venv*/
env*/
.venv*/

# Package files
*.tar.gz
*.zip
*.rar

# Editor files
*.sublime-project
*.sublime-workspace

# Flask session files
flask_session/

# Cache directories
.cache/
__pycache__/
.pytest_cache/

# Node.js (if using any frontend tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build files
build/
dist/

# Deployment files
.deployment
deploy.sh

# Security files
*.key
*.pem
*.crt
secrets/

# Large dataset files
datasets/
data/
*.csv
*.json
*.xml

# Jupyter notebooks
*.ipynb

# PyTorch model checkpoints
*.pth
*.pkl

# TensorFlow model files
*.pb
*.h5

# ONNX model files
*.onnx

# Model training outputs
runs/
wandb/
tensorboard/

# Experiment tracking
mlruns/
.mlflow/

# Docker files (optional)
.dockerignore
Dockerfile.dev

# Kubernetes files (optional)
k8s/
*.yaml
*.yml

# Terraform files (optional)
*.tfstate
*.tfstate.backup
.terraform/

# AWS files (optional)
.aws/

# Google Cloud files (optional)
.gcloud/

# Azure files (optional)
.azure/
