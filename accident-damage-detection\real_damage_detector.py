#!/usr/bin/env python3
"""
Real damage detection algorithm using computer vision
"""

import cv2
import numpy as np
from dataclasses import dataclass
from typing import List, Tuple, Optional
import os

@dataclass
class RealDamageDetection:
    """Real damage detection result"""
    part_name: str
    severity: str
    confidence: float
    description: str
    repair_needed: bool
    estimated_area_percentage: float
    bbox: Tuple[int, int, int, int]  # x, y, width, height
    damage_type: str  # 'scratch', 'dent', 'crack', 'missing_part'

class RealDamageDetector:
    """Real computer vision-based damage detector"""
    
    def __init__(self):
        """Initialize the real damage detector"""
        self.vehicle_parts = {
            'bumper': [(0.1, 0.7, 0.8, 0.3), (0.1, 0.0, 0.8, 0.3)],  # front and rear bumper regions
            'door': [(0.0, 0.3, 0.3, 0.4), (0.7, 0.3, 0.3, 0.4)],     # left and right door regions
            'hood': [(0.2, 0.0, 0.6, 0.4)],                           # hood region
            'fender': [(0.0, 0.2, 0.2, 0.3), (0.8, 0.2, 0.2, 0.3)],  # left and right fender
            'light': [(0.1, 0.1, 0.2, 0.2), (0.7, 0.1, 0.2, 0.2)],   # headlight regions
            'windshield': [(0.3, 0.1, 0.4, 0.3)]                     # windshield region
        }
        
        # Damage detection parameters
        self.scratch_threshold = 30
        self.dent_threshold = 40
        self.crack_threshold = 25
        
    def detect_damage(self, image_path: str) -> List[RealDamageDetection]:
        """Detect real damage in the image using computer vision"""
        
        print(f"🔍 Starting real damage detection on: {image_path}")
        
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Could not load image: {image_path}")
            return []
        
        height, width = image.shape[:2]
        print(f"📐 Image dimensions: {width}x{height}")
        
        detections = []
        
        # Convert to different color spaces for analysis
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Detect different types of damage
        detections.extend(self._detect_scratches(image, gray, width, height))
        detections.extend(self._detect_dents(image, gray, width, height))
        detections.extend(self._detect_cracks(image, gray, width, height))
        detections.extend(self._detect_color_anomalies(image, hsv, width, height))
        
        print(f"✅ Real detection complete: {len(detections)} damage areas found")
        
        return detections
    
    def _detect_scratches(self, image: np.ndarray, gray: np.ndarray, width: int, height: int) -> List[RealDamageDetection]:
        """Detect scratches using edge detection"""
        
        detections = []
        
        # Apply Canny edge detection
        edges = cv2.Canny(gray, 50, 150)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # Filter by area (scratches are typically elongated)
            if 100 < area < 5000:
                x, y, w, h = cv2.boundingRect(contour)
                
                # Check aspect ratio (scratches are usually longer than wide)
                aspect_ratio = max(w, h) / min(w, h)
                
                if aspect_ratio > 3:  # Elongated shape indicates potential scratch
                    part_name = self._identify_part_region(x + w/2, y + h/2, width, height)
                    
                    # Calculate confidence based on edge strength
                    roi = edges[y:y+h, x:x+w]
                    edge_density = np.sum(roi > 0) / (w * h)
                    confidence = min(0.9, edge_density * 2)
                    
                    if confidence > 0.3:
                        severity = self._calculate_severity(area, width * height)
                        
                        detection = RealDamageDetection(
                            part_name=part_name,
                            severity=severity,
                            confidence=confidence,
                            description=f"Linear scratch detected on {part_name.lower()}",
                            repair_needed=confidence > 0.5,
                            estimated_area_percentage=(area / (width * height)) * 100,
                            bbox=(x, y, w, h),
                            damage_type='scratch'
                        )
                        detections.append(detection)
        
        return detections
    
    def _detect_dents(self, image: np.ndarray, gray: np.ndarray, width: int, height: int) -> List[RealDamageDetection]:
        """Detect dents using shadow/highlight analysis"""
        
        detections = []
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (15, 15), 0)
        
        # Find areas with unusual shadow patterns (potential dents)
        # Use morphological operations to find circular/oval shapes
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (20, 20))
        tophat = cv2.morphologyEx(blurred, cv2.MORPH_TOPHAT, kernel)
        blackhat = cv2.morphologyEx(blurred, cv2.MORPH_BLACKHAT, kernel)
        
        # Combine tophat and blackhat to find dent-like features
        dent_features = cv2.add(tophat, blackhat)
        
        # Threshold to find significant features
        _, thresh = cv2.threshold(dent_features, 30, 255, cv2.THRESH_BINARY)
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            
            if 500 < area < 10000:  # Reasonable dent size
                x, y, w, h = cv2.boundingRect(contour)
                
                # Check if shape is roughly circular (dents are often round)
                aspect_ratio = w / h
                if 0.5 < aspect_ratio < 2.0:  # Not too elongated
                    part_name = self._identify_part_region(x + w/2, y + h/2, width, height)
                    
                    # Calculate confidence based on feature strength
                    roi = dent_features[y:y+h, x:x+w]
                    feature_strength = np.mean(roi)
                    confidence = min(0.85, feature_strength / 50)
                    
                    if confidence > 0.4:
                        severity = self._calculate_severity(area, width * height)
                        
                        detection = RealDamageDetection(
                            part_name=part_name,
                            severity=severity,
                            confidence=confidence,
                            description=f"Dent detected on {part_name.lower()}",
                            repair_needed=True,
                            estimated_area_percentage=(area / (width * height)) * 100,
                            bbox=(x, y, w, h),
                            damage_type='dent'
                        )
                        detections.append(detection)
        
        return detections
    
    def _detect_cracks(self, image: np.ndarray, gray: np.ndarray, width: int, height: int) -> List[RealDamageDetection]:
        """Detect cracks using line detection"""
        
        detections = []
        
        # Use HoughLinesP to detect line segments (potential cracks)
        edges = cv2.Canny(gray, 50, 150)
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=30, maxLineGap=10)
        
        if lines is not None:
            # Group nearby lines that might represent the same crack
            crack_groups = self._group_lines(lines, width, height)
            
            for group in crack_groups:
                if len(group) >= 2:  # Multiple line segments suggest a crack
                    # Calculate bounding box for the crack group
                    all_points = []
                    for line in group:
                        x1, y1, x2, y2 = line[0]
                        all_points.extend([(x1, y1), (x2, y2)])
                    
                    if all_points:
                        xs, ys = zip(*all_points)
                        x, y = min(xs), min(ys)
                        w, h = max(xs) - x, max(ys) - y
                        
                        part_name = self._identify_part_region(x + w/2, y + h/2, width, height)
                        
                        # Calculate confidence based on number of line segments
                        confidence = min(0.8, len(group) * 0.2)
                        
                        if confidence > 0.3:
                            # Cracks are usually severe damage
                            severity = 'severe' if confidence > 0.6 else 'moderate'
                            
                            detection = RealDamageDetection(
                                part_name=part_name,
                                severity=severity,
                                confidence=confidence,
                                description=f"Crack detected on {part_name.lower()}",
                                repair_needed=True,
                                estimated_area_percentage=((w * h) / (width * height)) * 100,
                                bbox=(x, y, w, h),
                                damage_type='crack'
                            )
                            detections.append(detection)
        
        return detections
    
    def _detect_color_anomalies(self, image: np.ndarray, hsv: np.ndarray, width: int, height: int) -> List[RealDamageDetection]:
        """Detect color anomalies that might indicate damage"""
        
        detections = []
        
        # Define color ranges for rust, paint damage, etc.
        # Rust colors (orange/brown)
        rust_lower = np.array([10, 50, 50])
        rust_upper = np.array([25, 255, 255])
        rust_mask = cv2.inRange(hsv, rust_lower, rust_upper)
        
        # Find rust-colored areas
        contours, _ = cv2.findContours(rust_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            
            if area > 200:  # Significant rust area
                x, y, w, h = cv2.boundingRect(contour)
                part_name = self._identify_part_region(x + w/2, y + h/2, width, height)
                
                # Calculate confidence based on area and color consistency
                roi_mask = rust_mask[y:y+h, x:x+w]
                color_consistency = np.sum(roi_mask > 0) / (w * h)
                confidence = min(0.7, color_consistency)
                
                if confidence > 0.3:
                    severity = self._calculate_severity(area, width * height)
                    
                    detection = RealDamageDetection(
                        part_name=part_name,
                        severity=severity,
                        confidence=confidence,
                        description=f"Rust/corrosion detected on {part_name.lower()}",
                        repair_needed=True,
                        estimated_area_percentage=(area / (width * height)) * 100,
                        bbox=(x, y, w, h),
                        damage_type='rust'
                    )
                    detections.append(detection)
        
        return detections
    
    def _identify_part_region(self, center_x: float, center_y: float, width: int, height: int) -> str:
        """Identify which vehicle part the damage is on based on location"""
        
        # Normalize coordinates
        norm_x = center_x / width
        norm_y = center_y / height
        
        # Check each part region
        for part_name, regions in self.vehicle_parts.items():
            for region in regions:
                rx, ry, rw, rh = region
                if rx <= norm_x <= rx + rw and ry <= norm_y <= ry + rh:
                    return part_name.title()
        
        # Default to generic part name based on location
        if norm_y < 0.3:
            return "Hood"
        elif norm_y > 0.7:
            return "Bumper"
        elif norm_x < 0.3:
            return "Left Side"
        elif norm_x > 0.7:
            return "Right Side"
        else:
            return "Body Panel"
    
    def _calculate_severity(self, damage_area: float, total_area: float) -> str:
        """Calculate damage severity based on area"""
        
        percentage = (damage_area / total_area) * 100
        
        if percentage > 5:
            return 'severe'
        elif percentage > 1:
            return 'moderate'
        else:
            return 'minor'
    
    def _group_lines(self, lines: np.ndarray, width: int, height: int) -> List[List]:
        """Group nearby lines that might represent the same crack"""
        
        groups = []
        used = set()
        
        for i, line1 in enumerate(lines):
            if i in used:
                continue
                
            group = [line1]
            used.add(i)
            
            x1, y1, x2, y2 = line1[0]
            
            for j, line2 in enumerate(lines):
                if j in used:
                    continue
                    
                x3, y3, x4, y4 = line2[0]
                
                # Check if lines are close to each other
                dist1 = np.sqrt((x1 - x3)**2 + (y1 - y3)**2)
                dist2 = np.sqrt((x2 - x4)**2 + (y2 - y4)**2)
                
                if dist1 < 50 or dist2 < 50:  # Lines are close
                    group.append(line2)
                    used.add(j)
            
            groups.append(group)
        
        return groups
    
    def create_detection_visualization(self, image_path: str, detections: List[RealDamageDetection]) -> Optional[np.ndarray]:
        """Create visualization of real damage detections"""
        
        try:
            image = cv2.imread(image_path)
            if image is None:
                return None
            
            height, width = image.shape[:2]
            
            # Create overlay for damage areas
            overlay = image.copy()
            
            # Color mapping for damage types
            damage_colors = {
                'scratch': (0, 255, 255),    # Yellow
                'dent': (0, 165, 255),       # Orange
                'crack': (0, 0, 255),        # Red
                'rust': (0, 100, 200)        # Dark orange
            }
            
            # Add header
            cv2.rectangle(image, (0, 0), (width, 80), (0, 0, 0), -1)
            cv2.putText(image, "REAL AI DAMAGE DETECTION", (width//2 - 200, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)
            
            # Draw detection boxes and labels
            y_offset = 100
            for i, detection in enumerate(detections):
                color = damage_colors.get(detection.damage_type, (0, 255, 0))
                
                # Draw bounding box
                x, y, w, h = detection.bbox
                cv2.rectangle(image, (x, y), (x + w, y + h), color, 2)
                
                # Add detection number
                cv2.putText(image, str(i + 1), (x, y - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)
                
                # Add label on the side
                label = f"{i+1}. {detection.part_name}: {detection.damage_type.upper()} ({detection.confidence:.0%})"
                
                # Background for text
                text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                cv2.rectangle(image, (10, y_offset - 25), (10 + text_size[0] + 10, y_offset + 10), color, -1)
                cv2.rectangle(image, (10, y_offset - 25), (10 + text_size[0] + 10, y_offset + 10), (255, 255, 255), 2)
                
                # Add text
                cv2.putText(image, label, (15, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                y_offset += 40
            
            # Add footer
            if detections:
                cv2.rectangle(image, (0, height - 60), (width, height), (0, 0, 0), -1)
                summary = f"REAL DAMAGE DETECTED: {len(detections)} AREAS"
                avg_confidence = sum(d.confidence for d in detections) / len(detections)
                confidence_text = f"AVERAGE CONFIDENCE: {avg_confidence:.0%}"
                
                cv2.putText(image, summary, (width//2 - 150, height - 35), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                cv2.putText(image, confidence_text, (width//2 - 120, height - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)
            
            return image
            
        except Exception as e:
            print(f"❌ Error creating visualization: {e}")
            return None

if __name__ == "__main__":
    # Test the real damage detector
    detector = RealDamageDetector()
    
    # Test with a sample image
    test_image = "static/uploaded_image.jpg"
    if os.path.exists(test_image):
        detections = detector.detect_damage(test_image)
        
        print(f"\n🎯 REAL DAMAGE DETECTION RESULTS:")
        print(f"Found {len(detections)} damage areas:")
        
        for detection in detections:
            print(f"  • {detection.part_name}: {detection.damage_type} ({detection.confidence:.1%})")
        
        # Create visualization
        viz = detector.create_detection_visualization(test_image, detections)
        if viz is not None:
            cv2.imwrite("static/detected_image.jpg", viz)
            print(f"✅ Real detection visualization saved")
    else:
        print(f"❌ Test image not found: {test_image}")
