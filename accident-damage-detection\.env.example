# Environment Variables for LLM-Powered Vehicle Damage Detection

# Flask Secret Key (change this in production)
SECRET_KEY=your_secret_key_here_change_this_in_production

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# LLM Configuration
LLM_MODEL=gpt-4o
LLM_CONFIDENCE_THRESHOLD=0.4
LLM_MAX_TOKENS=1000
LLM_TEMPERATURE=0.1

# Database Configuration (if using MySQL instead of SQLite)
# MYSQL_HOST=127.0.0.1
# MYSQL_USER=root
# MYSQL_PASSWORD=Root@123
# MYSQL_DATABASE=vehicle_damage_detection
