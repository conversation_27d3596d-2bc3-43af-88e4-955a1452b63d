import mysql.connector
from mysql.connector import Error

try:
    connection = mysql.connector.connect(
        host='localhost',
        database='vehicle_damage_detection',
        user='root',
        password='Root@123'
    )

    if connection.is_connected():
        print("Successfully connected to MySQL database")

except Error as e:
    print(f"Error: {e}")

finally:
    if 'connection' in locals() and connection.is_connected():
        connection.close()
        print("MySQL connection closed")