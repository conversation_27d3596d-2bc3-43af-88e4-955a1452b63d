#!/usr/bin/env python3
"""
Simple, Working Damage Detector - Fixes both issues:
1. Door damage showing as bumper damage
2. Real damage showing as "no significant damage"
"""

import os
import json
import base64
import requests
from typing import List, Dict, Any
from dataclasses import dataclass

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    except Exception:
        pass

@dataclass
class DamageDetection:
    part_name: str
    severity: str
    confidence: float
    description: str
    repair_needed: bool
    estimated_area_percentage: float

class SimpleWorkingDetector:
    def __init__(self):
        self.gemini_api_key = os.getenv('GEMINI_API_KEY', 'AIzaSyCDZFr703ty5gIwsrAXn3B5CTicASdF-nI')
        self.openai_api_key = os.getenv('OPENAI_API_KEY', 'your_openai_api_key_here')
        
        print(f"🔧 Simple Working Detector initialized")
        print(f"   Gemini: {'✅' if self.gemini_api_key != 'your_gemini_api_key_here' else '❌'}")
        print(f"   OpenAI: {'✅' if self.openai_api_key != 'your_openai_api_key_here' else '❌'}")

    def encode_image_base64(self, image_path: str) -> str:
        """Encode image to base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def create_simple_prompt(self) -> str:
        """Simple, clear prompt that works"""
        return """
Analyze this vehicle image for damage. Be practical and accurate.

IMPORTANT RULES:
1. DOORS are on the SIDES of the vehicle (left/right)
2. BUMPERS are on the FRONT/REAR of the vehicle
3. Report damage you can clearly see
4. Don't be overly strict - report obvious damage

Vehicle parts: Door, Front Bumper, Rear Bumper, Hood, Fender, Headlight, Taillight, Windshield

For each damaged part, respond with JSON:
[
  {
    "part_name": "Door",
    "severity": "moderate",
    "confidence": 0.8,
    "description": "Dent on driver door",
    "repair_needed": true,
    "estimated_area_percentage": 25.0
  }
]

If no damage visible, respond: []

REMEMBER: 
- Side damage = Door
- Front/rear damage = Bumper
- Report damage you can see
"""

    def analyze_with_gemini(self, image_path: str) -> List[DamageDetection]:
        """Simple Gemini analysis"""
        try:
            base64_image = self.encode_image_base64(image_path)
            
            url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={self.gemini_api_key}"
            
            headers = {"Content-Type": "application/json"}
            
            data = {
                "contents": [
                    {
                        "parts": [
                            {"text": self.create_simple_prompt()},
                            {
                                "inline_data": {
                                    "mime_type": "image/jpeg",
                                    "data": base64_image
                                }
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 1000
                }
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text'].strip()
                    print(f"🤖 Gemini response: {content}")
                    
                    # Parse JSON - handle markdown wrapping
                    json_content = content
                    if '```json' in content:
                        # Extract JSON from markdown
                        start = content.find('[')
                        end = content.rfind(']') + 1
                        if start != -1 and end != 0:
                            json_content = content[start:end]

                    if json_content.startswith('[') and json_content.endswith(']'):
                        detections_data = json.loads(json_content)
                        detections = []
                        for item in detections_data:
                            # Fix part names immediately
                            fixed_part_name = self.fix_part_name(item['part_name'], item['description'])
                            
                            detection = DamageDetection(
                                part_name=fixed_part_name,
                                severity=item['severity'],
                                confidence=float(item['confidence']),
                                description=item['description'],
                                repair_needed=item['repair_needed'],
                                estimated_area_percentage=float(item['estimated_area_percentage'])
                            )
                            detections.append(detection)
                        return detections
                    else:
                        print("⚠️ Non-JSON response from Gemini")
                        return []
                else:
                    print("⚠️ Empty response from Gemini")
                    return []
            else:
                print(f"❌ Gemini API error: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Gemini analysis failed: {e}")
            return []

    def analyze_with_openai(self, image_path: str) -> List[DamageDetection]:
        """Simple OpenAI analysis"""
        try:
            from openai import OpenAI

            if self.openai_api_key == "your_openai_api_key_here":
                print("⚠️ OpenAI API key not configured")
                return []

            client = OpenAI(api_key=self.openai_api_key)
            base64_image = self.encode_image_base64(image_path)

            response = client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.create_simple_prompt()},
                            {
                                "type": "image_url",
                                "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )
            
            content = response.choices[0].message.content.strip()
            print(f"🤖 OpenAI response: {content}")
            
            # Parse JSON - handle markdown wrapping
            json_content = content
            if '```json' in content:
                # Extract JSON from markdown
                start = content.find('[')
                end = content.rfind(']') + 1
                if start != -1 and end != 0:
                    json_content = content[start:end]

            if json_content.startswith('[') and json_content.endswith(']'):
                detections_data = json.loads(json_content)
                detections = []
                for item in detections_data:
                    # Fix part names immediately
                    fixed_part_name = self.fix_part_name(item['part_name'], item['description'])
                    
                    detection = DamageDetection(
                        part_name=fixed_part_name,
                        severity=item['severity'],
                        confidence=float(item['confidence']),
                        description=item['description'],
                        repair_needed=item['repair_needed'],
                        estimated_area_percentage=float(item['estimated_area_percentage'])
                    )
                    detections.append(detection)
                return detections
            else:
                print("⚠️ Non-JSON response from OpenAI")
                return []
                
        except Exception as e:
            print(f"❌ OpenAI analysis failed: {e}")
            return []

    def fix_part_name(self, part_name: str, description: str) -> str:
        """Simple, direct part name fixing"""
        
        part_lower = part_name.lower()
        desc_lower = description.lower()
        
        print(f"🔧 Checking part name: '{part_name}' with description: '{description[:50]}...'")
        
        # Check for door indicators in description
        door_words = ['door', 'side', 'passenger', 'driver', 'handle', 'window', 'panel']
        has_door_words = any(word in desc_lower for word in door_words)
        
        # Check for bumper indicators in description
        bumper_words = ['front', 'rear', 'grille', 'license', 'headlight', 'taillight']
        has_bumper_words = any(word in desc_lower for word in bumper_words)
        
        # Fix obvious misclassifications
        if 'bumper' in part_lower and has_door_words and not has_bumper_words:
            print(f"🔧 FIXED: '{part_name}' → 'Door' (description indicates door)")
            return 'Door'
        
        if 'door' in part_lower and has_bumper_words and not has_door_words:
            print(f"🔧 FIXED: '{part_name}' → 'Front Bumper' (description indicates bumper)")
            return 'Front Bumper'
        
        # Normalize to standard names
        if 'door' in part_lower:
            return 'Door'
        elif 'front' in part_lower and 'bumper' in part_lower:
            return 'Front Bumper'
        elif 'rear' in part_lower and 'bumper' in part_lower:
            return 'Rear Bumper'
        elif 'bumper' in part_lower:
            return 'Front Bumper'
        elif 'hood' in part_lower:
            return 'Hood'
        elif 'fender' in part_lower:
            return 'Fender'
        elif 'headlight' in part_lower:
            return 'Headlight'
        elif 'taillight' in part_lower:
            return 'Taillight'
        elif 'windshield' in part_lower:
            return 'Windshield'
        
        print(f"✅ Part name OK: '{part_name}'")
        return part_name

    def analyze_image(self, image_path: str) -> List[DamageDetection]:
        """Main analysis method - simple and direct"""
        
        print(f"🔍 Simple analysis starting: {image_path}")
        
        # Try Gemini first
        detections = self.analyze_with_gemini(image_path)
        
        # If Gemini fails, try OpenAI
        if not detections:
            print("🔄 Gemini failed, trying OpenAI...")
            detections = self.analyze_with_openai(image_path)
        
        # Apply final validation
        validated = self.simple_validation(detections)
        
        print(f"✅ Simple analysis complete: {len(validated)} damage areas found")
        for detection in validated:
            print(f"   • {detection.part_name}: {detection.confidence:.1%} - {detection.description}")
        
        return validated

    def simple_validation(self, detections: List[DamageDetection]) -> List[DamageDetection]:
        """Simple validation - not too strict"""
        
        if not detections:
            return []
        
        validated = []
        
        for detection in detections:
            # Basic confidence check - not too strict
            if detection.confidence < 0.6:
                print(f"🚫 Rejected {detection.part_name}: Low confidence ({detection.confidence:.1%})")
                continue
            
            # Basic area check - not too strict
            if detection.estimated_area_percentage < 5:
                print(f"🚫 Rejected {detection.part_name}: Too small area ({detection.estimated_area_percentage}%)")
                continue
            
            # Accept the detection
            validated.append(detection)
            print(f"✅ Accepted {detection.part_name}: {detection.confidence:.1%}")
        
        return validated

if __name__ == "__main__":
    print("🔧 SIMPLE WORKING DETECTOR TEST")
    print("=" * 60)
    
    detector = SimpleWorkingDetector()
    
    # Test with a sample image if available
    test_image = "static/uploaded_image.jpg"
    if os.path.exists(test_image):
        print(f"\n🔍 Testing with: {test_image}")
        results = detector.analyze_image(test_image)
        
        if results:
            print(f"\n📊 RESULTS: {len(results)} damage areas detected")
            for result in results:
                print(f"   • {result.part_name}: {result.severity} ({result.confidence:.1%})")
                print(f"     Description: {result.description}")
        else:
            print("\n📊 RESULTS: No damage detected")
    else:
        print(f"\n⚠️ Test image not found: {test_image}")
        print("Upload an image through the web interface to test")
