#!/usr/bin/env python3
"""
Smart Damage Validation System - Improves accuracy without new API keys
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
import os

@dataclass
class DamageDetection:
    part_name: str
    severity: str
    confidence: float
    description: str
    repair_needed: bool
    estimated_area_percentage: float

class SmartDamageValidator:
    def __init__(self):
        self.false_positive_patterns = {
            'bumper': [
                'edge', 'line', 'seam', 'gap', 'styling', 'design',
                'shadow', 'reflection', 'normal', 'typical'
            ],
            'door': [
                'handle', 'trim', 'molding', 'edge', 'seam'
            ],
            'general': [
                'appears', 'seems', 'might', 'could', 'possibly',
                'potentially', 'looks like', 'minor wear'
            ]
        }
        
        self.damage_keywords = {
            'high_confidence': [
                'cracked', 'broken', 'shattered', 'missing', 'hole',
                'crushed', 'smashed', 'bent', 'detached', 'hanging',
                'torn', 'split', 'punctured'
            ],
            'medium_confidence': [
                'dent', 'dented', 'scratched', 'damaged', 'impact',
                'collision', 'rust', 'corrosion', 'chipped'
            ],
            'low_confidence': [
                'mark', 'scuff', 'wear', 'discoloration'
            ]
        }

    def analyze_image_features(self, image_path: str) -> Dict[str, Any]:
        """Analyze image features to help validate damage claims"""
        try:
            # Read image
            image = cv2.imread(image_path)
            if image is None:
                return {}
            
            # Convert to different color spaces for analysis
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Analyze image quality and features
            features = {
                'brightness': np.mean(gray),
                'contrast': np.std(gray),
                'sharpness': cv2.Laplacian(gray, cv2.CV_64F).var(),
                'edges': len(cv2.Canny(gray, 50, 150).nonzero()[0]),
                'image_quality': 'good' if cv2.Laplacian(gray, cv2.CV_64F).var() > 100 else 'poor'
            }
            
            return features
            
        except Exception as e:
            print(f"⚠️ Image analysis failed: {e}")
            return {}

    def validate_detection_consistency(self, detections: List[DamageDetection]) -> List[DamageDetection]:
        """Check for consistency in damage detections"""
        if not detections:
            return []
        
        validated = []
        
        for detection in detections:
            # Check for description consistency
            description_lower = detection.description.lower()
            
            # Calculate description quality score
            quality_score = self.calculate_description_quality(description_lower)
            
            # Adjust confidence based on description quality
            adjusted_confidence = detection.confidence * quality_score
            
            # Apply smart thresholds
            if self.should_accept_detection(detection, adjusted_confidence, description_lower):
                # Update confidence with adjusted value
                detection.confidence = min(adjusted_confidence, 1.0)
                validated.append(detection)
                print(f"✅ Validated: {detection.part_name} (adjusted confidence: {adjusted_confidence:.1%})")
            else:
                print(f"🚫 Rejected: {detection.part_name} (quality score: {quality_score:.2f})")
        
        return validated

    def calculate_description_quality(self, description: str) -> float:
        """Calculate quality score for damage description"""
        score = 1.0
        
        # Check for false positive patterns
        for pattern in self.false_positive_patterns['general']:
            if pattern in description:
                score *= 0.7  # Reduce score for vague language
        
        # Check for specific damage keywords
        has_high_conf_keyword = any(kw in description for kw in self.damage_keywords['high_confidence'])
        has_med_conf_keyword = any(kw in description for kw in self.damage_keywords['medium_confidence'])
        has_low_conf_keyword = any(kw in description for kw in self.damage_keywords['low_confidence'])
        
        if has_high_conf_keyword:
            score *= 1.2  # Boost for specific damage terms
        elif has_med_conf_keyword:
            score *= 1.1  # Slight boost for medium confidence terms
        elif has_low_conf_keyword:
            score *= 0.9  # Slight reduction for low confidence terms
        else:
            score *= 0.8  # Reduction for no specific damage terms
        
        # Check description length and specificity
        if len(description.split()) < 4:
            score *= 0.8  # Reduce for very short descriptions
        elif len(description.split()) > 8:
            score *= 1.1  # Boost for detailed descriptions
        
        return min(score, 1.5)  # Cap at 1.5x

    def should_accept_detection(self, detection: DamageDetection, adjusted_confidence: float, description: str) -> bool:
        """Smart decision on whether to accept a detection"""
        
        # Base confidence threshold
        min_confidence = 0.65
        
        # Special handling for bumper (your main issue)
        if 'bumper' in detection.part_name.lower():
            # Much stricter for bumpers
            min_confidence = 0.8
            
            # Check for bumper-specific false positive patterns
            bumper_false_positives = self.false_positive_patterns['bumper']
            if any(pattern in description for pattern in bumper_false_positives):
                print(f"🚫 Bumper rejected: Contains false positive pattern")
                return False
            
            # Require high-confidence damage keywords for bumpers
            has_strong_damage = any(kw in description for kw in self.damage_keywords['high_confidence'])
            if not has_strong_damage and adjusted_confidence < 0.85:
                print(f"🚫 Bumper rejected: No strong damage indicators")
                return False
        
        # Check minimum confidence
        if adjusted_confidence < min_confidence:
            return False
        
        # Check for minimum damage area
        if detection.estimated_area_percentage < 5:
            return False
        
        # Additional validation for severity vs confidence consistency
        severity_confidence_map = {
            'minor': 0.65,
            'moderate': 0.75,
            'severe': 0.85
        }
        
        required_confidence = severity_confidence_map.get(detection.severity, 0.7)
        if adjusted_confidence < required_confidence:
            print(f"🚫 Rejected: Confidence {adjusted_confidence:.1%} too low for {detection.severity} damage")
            return False
        
        return True

    def smart_validate_detections(self, detections: List[DamageDetection], image_path: str) -> List[DamageDetection]:
        """Main validation method combining all smart checks"""
        
        if not detections:
            print("✅ No detections to validate")
            return []
        
        print(f"🧠 Smart validation of {len(detections)} detections...")
        
        # Analyze image features
        image_features = self.analyze_image_features(image_path)
        
        # Apply consistency validation
        validated = self.validate_detection_consistency(detections)
        
        # Apply image quality adjustments
        if image_features.get('image_quality') == 'poor':
            print("⚠️ Poor image quality detected - applying stricter validation")
            validated = [d for d in validated if d.confidence >= 0.8]
        
        # Final sanity check
        final_validated = self.final_sanity_check(validated)
        
        print(f"🎯 Smart validation: {len(detections)} → {len(final_validated)} detections")
        
        return final_validated

    def final_sanity_check(self, detections: List[DamageDetection]) -> List[DamageDetection]:
        """Final sanity check for obviously wrong detections"""
        
        if not detections:
            return []
        
        # Check for too many detections (likely false positives)
        if len(detections) > 5:
            print("⚠️ Too many detections - keeping only highest confidence")
            detections = sorted(detections, key=lambda x: x.confidence, reverse=True)[:3]
        
        # Check for duplicate parts
        seen_parts = set()
        unique_detections = []
        
        for detection in detections:
            part_key = detection.part_name.lower()
            if part_key not in seen_parts:
                seen_parts.add(part_key)
                unique_detections.append(detection)
            else:
                print(f"🚫 Removed duplicate detection: {detection.part_name}")
        
        return unique_detections

def test_smart_validator():
    """Test the smart validation system"""
    
    print("🧠 TESTING SMART DAMAGE VALIDATOR")
    print("=" * 50)
    
    validator = SmartDamageValidator()
    
    # Create test detections
    test_detections = [
        DamageDetection(
            part_name="Front Bumper",
            severity="minor",
            confidence=0.72,
            description="Minor surface marks visible on front bumper edge",
            repair_needed=True,
            estimated_area_percentage=8.0
        ),
        DamageDetection(
            part_name="Door",
            severity="moderate",
            confidence=0.85,
            description="Large dent with clear metal deformation on driver door",
            repair_needed=True,
            estimated_area_percentage=25.0
        ),
        DamageDetection(
            part_name="Front Bumper",
            severity="severe",
            confidence=0.92,
            description="Cracked bumper with visible hole from impact",
            repair_needed=True,
            estimated_area_percentage=35.0
        )
    ]
    
    print(f"📋 Testing with {len(test_detections)} detections:")
    for i, detection in enumerate(test_detections, 1):
        print(f"  {i}. {detection.part_name}: {detection.severity} ({detection.confidence:.1%})")
        print(f"     '{detection.description}'")
    
    # Validate detections
    validated = validator.validate_detection_consistency(test_detections)
    
    print(f"\n📊 Validation Results:")
    print(f"   Input: {len(test_detections)} detections")
    print(f"   Output: {len(validated)} detections")
    
    for detection in validated:
        print(f"   ✅ {detection.part_name}: {detection.confidence:.1%}")

if __name__ == "__main__":
    test_smart_validator()
