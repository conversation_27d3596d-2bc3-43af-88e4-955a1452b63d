#!/usr/bin/env python3
"""
Quick test of dashboard fixes
"""

import os

def quick_test():
    print("🔧 QUICK DASHBOARD FIX TEST")
    print("=" * 50)
    
    # Test 1: Check if we can import everything
    try:
        from app import app, get_part_prices
        from llm_damage_detector import LLMDamageDetector
        print("✅ All imports successful")
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Test 2: Check if static directory exists
    static_dir = "static"
    if not os.path.exists(static_dir):
        os.makedirs(static_dir)
        print("✅ Created static directory")
    else:
        print("✅ Static directory exists")
    
    # Test 3: Test pricing function
    try:
        test_damage = {'Bumper': 1, 'Door': 1}
        prices = get_part_prices("<EMAIL>", test_damage)
        if prices:
            total = sum(details['total'] for details in prices.values())
            print(f"✅ Pricing works: ₹{total:,}")
        else:
            print("❌ Pricing failed")
            return False
    except Exception as e:
        print(f"❌ Pricing test failed: {e}")
        return False
    
    # Test 4: Test detection
    try:
        detector = LLMDamageDetector()
        print("✅ Detector created")
        
        # Check if uploaded image exists
        uploaded_path = "static/uploaded_image.jpg"
        if os.path.exists(uploaded_path):
            print("✅ Uploaded image exists")
        else:
            print("⚠️ No uploaded image, creating test image...")
            import cv2
            import numpy as np
            img = np.ones((600, 800, 3), dtype=np.uint8) * 200
            cv2.putText(img, "TEST VEHICLE", (250, 300), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 2)
            cv2.imwrite(uploaded_path, img)
            print("✅ Test image created")
        
    except Exception as e:
        print(f"❌ Detection test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = quick_test()
    
    if success:
        print("\n🎉 QUICK TEST PASSED!")
        print("\n✅ Dashboard fixes should work:")
        print("   • Detected image will be created during analysis")
        print("   • Pricing information will be calculated")
        print("   • Both will display in dashboard")
        
        print("\n🚀 Starting app...")
        try:
            from app import app
            print("📍 URL: http://127.0.0.1:8080")
            print("🔑 Login: <EMAIL> / demo123")
            print("📸 Upload an image to test real detection!")
            app.run(debug=True, host='127.0.0.1', port=8080)
        except KeyboardInterrupt:
            print("\n👋 App stopped")
    else:
        print("\n❌ QUICK TEST FAILED!")
        print("Check the errors above")
