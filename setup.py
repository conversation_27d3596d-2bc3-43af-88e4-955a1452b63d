#!/usr/bin/env python3
"""
VehicleCare Pro Setup Script
Automated setup for the AI-powered vehicle damage assessment application
"""

import os
import sys
import subprocess
import platform

def print_banner():
    """Print the application banner"""
    print("=" * 60)
    print("🚗 VehicleCare Pro - Setup Script")
    print("AI-Powered Vehicle Damage Assessment")
    print("=" * 60)

def check_python_version():
    """Check if Python version is compatible"""
    print("\n📋 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"   Current version: {sys.version}")
        print("   Please upgrade Python and try again.")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} - Compatible")

def install_dependencies():
    """Install required Python packages"""
    print("\n📦 Installing dependencies...")
    
    requirements_file = os.path.join("accident-damage-detection", "requirements.txt")
    
    if not os.path.exists(requirements_file):
        print("❌ requirements.txt not found!")
        print("   Please ensure you're running this from the project root directory.")
        sys.exit(1)
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", requirements_file
        ])
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        print("   Please check your internet connection and try again.")
        sys.exit(1)

def setup_database():
    """Set up the SQLite database"""
    print("\n🗄️ Setting up database...")
    
    db_setup_script = os.path.join("accident-damage-detection", "setup_database.py")
    
    if not os.path.exists(db_setup_script):
        print("❌ Database setup script not found!")
        sys.exit(1)
    
    try:
        # Change to the accident-damage-detection directory
        original_dir = os.getcwd()
        os.chdir("accident-damage-detection")
        
        subprocess.check_call([sys.executable, "setup_database.py"])
        
        # Change back to original directory
        os.chdir(original_dir)
        
        print("✅ Database setup completed")
    except subprocess.CalledProcessError:
        print("❌ Failed to set up database")
        sys.exit(1)

def check_model_file():
    """Check if AI model file exists"""
    print("\n🤖 Checking AI model...")
    
    model_path = os.path.join("accident-damage-detection", "models", "best.pt")
    
    if os.path.exists(model_path):
        print("✅ AI model found")
        return True
    else:
        print("⚠️ AI model not found!")
        print("   Please place your YOLOv8 model file (best.pt) in:")
        print(f"   {os.path.abspath(os.path.join('accident-damage-detection', 'models'))}")
        print("   You can continue setup and add the model later.")
        return False

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        os.path.join("accident-damage-detection", "static"),
        os.path.join("accident-damage-detection", "models"),
        os.path.join("accident-damage-detection", "logs")
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("✅ Directories created")

def print_completion_message(model_exists):
    """Print setup completion message"""
    print("\n" + "=" * 60)
    print("🎉 VehicleCare Pro Setup Complete!")
    print("=" * 60)
    
    print("\n📋 Next Steps:")
    print("1. Navigate to the application directory:")
    print("   cd accident-damage-detection")
    
    if not model_exists:
        print("\n2. Add your AI model:")
        print("   - Place your YOLOv8 model file (best.pt) in the models/ directory")
        print("   - Or train a new model using your dataset")
    
    print(f"\n{'3' if not model_exists else '2'}. Start the application:")
    print("   python app.py")
    
    print(f"\n{'4' if not model_exists else '3'}. Open your browser:")
    print("   http://127.0.0.1:8080")
    
    print("\n🔗 Useful Links:")
    print("   📖 Documentation: README.md")
    print("   🤝 Contributing: CONTRIBUTING.md")
    print("   🐛 Issues: GitHub Issues page")
    
    print("\n🚗 Ready to analyze vehicle damage with AI! ✨")

def main():
    """Main setup function"""
    print_banner()
    
    # Check system requirements
    check_python_version()
    
    # Create necessary directories
    create_directories()
    
    # Install dependencies
    install_dependencies()
    
    # Set up database
    setup_database()
    
    # Check for AI model
    model_exists = check_model_file()
    
    # Print completion message
    print_completion_message(model_exists)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Setup failed with error: {e}")
        print("Please check the error message and try again.")
        sys.exit(1)
