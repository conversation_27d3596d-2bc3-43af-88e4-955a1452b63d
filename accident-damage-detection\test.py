import mysql.connector
from mysql.connector import Error
print("Before TRY")
try:
    connection = mysql.connector.connect(
        host='localhost',
        database='vehicle_damage_detection',
        user='root',
        password='Root@123',
        connection_timeout=1
    )

    if connection.is_connected():
        print("Successfully connected to MySQL database")

except Exception as e:
    print(f"Error: {e}")

finally:
    if 'connection' in locals() and connection.is_connected():
        connection.close()
        print("MySQL connection closed")