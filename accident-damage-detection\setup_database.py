#!/usr/bin/env python3
"""
Database setup script for Vehicle Damage Detection application.
This script creates the database, tables, and inserts sample data.
"""

import mysql.connector
import config
import json
import os

def setup_database():
    """Set up the database with schema and sample data."""
    
    # First, connect without specifying database to create it
    try:
        # Connect to MySQL server (without database)
        connection = mysql.connector.connect(
            host=config.mysql_credentials['host'],
            user=config.mysql_credentials['user'],
            password=config.mysql_credentials['password']
        )
        
        cursor = connection.cursor()
        
        # Create database
        cursor.execute("CREATE DATABASE IF NOT EXISTS vehicle_damage_detection")
        print("Database 'vehicle_damage_detection' created successfully!")
        
        cursor.close()
        connection.close()
        
    except mysql.connector.Error as e:
        print(f"Error creating database: {e}")
        return False
    
    # Now connect to the specific database and create tables
    try:
        connection = mysql.connector.connect(**config.mysql_credentials)
        cursor = connection.cursor()
        
        # Create user_info table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_info (
                user_id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                password VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL UNIQUE,
                vehicle_id VARCHAR(50) NOT NULL UNIQUE,
                contact_number VARCHAR(10) NOT NULL,
                address VARCHAR(100) NOT NULL,
                car_brand VARCHAR(100) NOT NULL,
                model VARCHAR(100) NOT NULL,
                registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create car_models table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS car_models (
                brand VARCHAR(50) NOT NULL,
                model VARCHAR(50) NOT NULL,
                part VARCHAR(50) NOT NULL,
                price INT NOT NULL
            )
        """)
        
        print("Tables created successfully!")
        
        # Check if car_models table has data
        cursor.execute("SELECT COUNT(*) FROM car_models")
        count = cursor.fetchone()[0]
        
        if count == 0:
            # Insert sample data from JSON file
            with open('car_parts_prices.json', 'r') as file:
                car_parts_prices = json.load(file)
            
            for brand, models in car_parts_prices.items():
                for model, parts in models.items():
                    for part, price in parts.items():
                        cursor.execute(
                            "INSERT INTO car_models (brand, model, part, price) VALUES (%s, %s, %s, %s)",
                            (brand, model, part, price)
                        )
            
            connection.commit()
            print("Sample data inserted successfully!")
        else:
            print(f"Car models table already has {count} records.")
        
        cursor.close()
        connection.close()
        
        print("Database setup completed successfully!")
        return True
        
    except mysql.connector.Error as e:
        print(f"Error setting up database: {e}")
        return False
    except FileNotFoundError:
        print("Error: car_parts_prices.json file not found!")
        return False

if __name__ == "__main__":
    print("Setting up Vehicle Damage Detection database...")
    success = setup_database()
    if success:
        print("\n✅ Database setup completed successfully!")
        print("You can now run the application with: python app.py")
    else:
        print("\n❌ Database setup failed!")
        print("Please check your MySQL connection and try again.")
