#!/usr/bin/env python3
"""
Verify that the API key fix is working
"""

import os
from dotenv import load_dotenv
from llm_damage_detector import LLMDamageDetector

def verify_api_fix():
    """Verify the API key is properly loaded"""
    
    print("🔍 VERIFYING API KEY FIX")
    print("=" * 40)
    
    # Load environment
    load_dotenv()
    
    # Check direct environment access
    api_key = os.getenv('OPENAI_API_KEY')
    print(f"📋 Environment API Key: {api_key[:15] if api_key else 'NOT_FOUND'}...")
    print(f"📏 Key Length: {len(api_key) if api_key else 0}")
    print(f"🔍 Is Placeholder: {api_key == 'your_openai_api_key_here' if api_key else 'No key'}")
    
    # Test LLM Detector initialization
    try:
        detector = LLMDamageDetector()
        print(f"✅ LLM Detector initialized successfully!")
        print(f"🔑 Detector API Key: {detector.api_key[:15]}...")
        print(f"🤖 Model: {detector.model}")
        print(f"📊 Confidence Threshold: {detector.confidence_threshold}")
        
        # Test if it's using real API key
        if detector.api_key == "your_openai_api_key_here":
            print("⚠️ Still using placeholder API key")
            return False
        else:
            print("✅ Using real API key!")
            return True
            
    except Exception as e:
        print(f"❌ LLM Detector failed: {e}")
        return False

def test_demo_detection():
    """Test demo detection to ensure pricing will work"""
    
    print("\n🧪 TESTING DEMO DETECTION")
    print("=" * 40)
    
    try:
        detector = LLMDamageDetector()
        
        # Get demo detections
        detections = detector._get_demo_detections()
        print(f"📋 Demo detections: {len(detections)}")
        
        # Test class counts
        class_counts = detector.get_part_counts_for_pricing(detections)
        print(f"🔢 Class counts: {class_counts}")
        
        if class_counts:
            print("✅ Demo detection and pricing conversion working!")
            return True
        else:
            print("❌ No class counts generated")
            return False
            
    except Exception as e:
        print(f"❌ Demo detection failed: {e}")
        return False

if __name__ == "__main__":
    api_ok = verify_api_fix()
    demo_ok = test_demo_detection()
    
    print("\n🎯 SUMMARY:")
    print(f"API Key Loading: {'✅ FIXED' if api_ok else '❌ FAILED'}")
    print(f"Demo Detection: {'✅ WORKING' if demo_ok else '❌ FAILED'}")
    
    if api_ok and demo_ok:
        print("\n🎉 ALL FIXES WORKING! Your app should now:")
        print("   • Load the real OpenAI API key")
        print("   • Use real LLM for damage detection")
        print("   • Provide pricing information")
        print("\n🚀 Start the app with: python app.py")
    else:
        print("\n⚠️ Some issues remain. Check the errors above.")
