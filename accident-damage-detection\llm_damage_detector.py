"""
LLM-based Vehicle Damage Detection Module
Replaces YOLO with GPT-4V for intelligent damage assessment
"""

import os
import json
import base64
import requests
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from PIL import Image
import cv2


@dataclass
class DamageDetection:
    """Represents a detected damage with details"""
    part_name: str
    severity: str  # 'minor', 'moderate', 'severe'
    confidence: float  # 0.0 to 1.0
    description: str
    repair_needed: bool
    estimated_area_percentage: float  # percentage of part affected


class LLMDamageDetector:
    """LLM-based damage detection using GPT-4V"""
    
    def __init__(self, api_key: str = None):
        """Initialize the LLM damage detector"""
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable.")
        
        self.base_url = "https://api.openai.com/v1/chat/completions"
        self.model = "gpt-4o"  # GPT-4 with vision capabilities
        
        # Vehicle parts that can be damaged
        self.vehicle_parts = [
            'Bonnet', 'Bumper', '<PERSON><PERSON>', 'Door', 'Fender', 'Light', 'Windshield'
        ]
        
        # Confidence threshold for reporting damage
        self.confidence_threshold = 0.4
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """Convert image to base64 for API transmission"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise Exception(f"Error encoding image: {e}")
    
    def create_damage_detection_prompt(self) -> str:
        """Create a detailed prompt for damage detection"""
        return f"""
You are an expert vehicle damage assessor. Analyze this vehicle image and detect any damage to the following parts:
{', '.join(self.vehicle_parts)}

For each damaged part you identify, provide:
1. Part name (exactly one of: {', '.join(self.vehicle_parts)})
2. Severity level (minor/moderate/severe)
3. Confidence score (0.0 to 1.0)
4. Brief description of the damage
5. Whether repair is needed (true/false)
6. Estimated percentage of part affected (0-100)

IMPORTANT GUIDELINES:
- Only report damage that is clearly visible and significant
- Be conservative - don't report minor scratches or normal wear
- Focus on structural damage, dents, cracks, missing parts, or significant paint damage
- Confidence should reflect how certain you are about the damage
- Use confidence < 0.4 for uncertain cases
- Use confidence 0.4-0.6 for likely damage
- Use confidence 0.6-0.8 for clear damage
- Use confidence 0.8+ for obvious severe damage

Respond ONLY with a JSON array in this exact format:
[
  {{
    "part_name": "Door",
    "severity": "moderate",
    "confidence": 0.75,
    "description": "Large dent on front passenger door",
    "repair_needed": true,
    "estimated_area_percentage": 25.0
  }}
]

If no damage is detected, respond with an empty array: []
"""
    
    def analyze_image(self, image_path: str) -> List[DamageDetection]:
        """Analyze image for vehicle damage using GPT-4V"""
        try:
            # Check if API key is placeholder - if so, use demo mode
            if self.api_key == "your_openai_api_key_here":
                print("🔧 Demo mode: Using simulated damage detection (API key not configured)")
                return self._get_demo_detections()

            # Encode image
            base64_image = self.encode_image_to_base64(image_path)

            # Prepare API request
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": self.create_damage_detection_prompt()
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.1  # Low temperature for consistent results
            }

            # Make API request
            response = requests.post(self.base_url, headers=headers, json=payload)
            response.raise_for_status()

            # Parse response
            result = response.json()
            content = result['choices'][0]['message']['content']

            # Parse JSON response
            try:
                damage_data = json.loads(content.strip())
                detections = []

                for item in damage_data:
                    detection = DamageDetection(
                        part_name=item['part_name'],
                        severity=item['severity'],
                        confidence=float(item['confidence']),
                        description=item['description'],
                        repair_needed=bool(item['repair_needed']),
                        estimated_area_percentage=float(item['estimated_area_percentage'])
                    )
                    detections.append(detection)

                return detections

            except json.JSONDecodeError as e:
                print(f"Error parsing LLM response: {e}")
                print(f"Raw response: {content}")
                return []

        except Exception as e:
            print(f"Error in LLM damage analysis: {e}")
            # If API fails, fall back to demo mode
            print("🔧 Falling back to demo mode due to API error")
            return self._get_demo_detections()

    def _get_demo_detections(self) -> List[DamageDetection]:
        """Generate demo damage detections for testing"""
        demo_detections = [
            DamageDetection(
                part_name="Door",
                severity="moderate",
                confidence=0.75,
                description="Visible dent on front passenger door with paint scratches",
                repair_needed=True,
                estimated_area_percentage=25.0
            ),
            DamageDetection(
                part_name="Bumper",
                severity="minor",
                confidence=0.65,
                description="Small scratches on front bumper, paint damage visible",
                repair_needed=True,
                estimated_area_percentage=15.0
            ),
            DamageDetection(
                part_name="Light",
                severity="severe",
                confidence=0.85,
                description="Cracked headlight lens, replacement needed",
                repair_needed=True,
                estimated_area_percentage=80.0
            )
        ]

        print("🎭 Demo detections generated:")
        for detection in demo_detections:
            print(f"   {detection.part_name}: {detection.severity} ({detection.confidence:.1%})")

        return demo_detections
    
    def evaluate_response_quality(self, detections: List[DamageDetection]) -> Dict[str, float]:
        """Evaluate the quality of LLM response"""
        if not detections:
            return {"overall_quality": 1.0, "confidence_consistency": 1.0, "severity_alignment": 1.0}

        # Check confidence consistency (penalize if all confidences are too similar)
        confidences = [d.confidence for d in detections]
        confidence_variance = np.var(confidences) if len(confidences) > 1 else 0.5
        confidence_consistency = min(1.0, confidence_variance * 2)  # Normalize to 0-1

        # Check severity-confidence alignment (severe damage should have high confidence)
        severity_alignment_scores = []
        for detection in detections:
            if detection.severity == 'severe' and detection.confidence >= 0.7:
                severity_alignment_scores.append(1.0)
            elif detection.severity == 'moderate' and 0.4 <= detection.confidence <= 0.8:
                severity_alignment_scores.append(1.0)
            elif detection.severity == 'minor' and detection.confidence <= 0.6:
                severity_alignment_scores.append(1.0)
            else:
                severity_alignment_scores.append(0.5)

        severity_alignment = np.mean(severity_alignment_scores) if severity_alignment_scores else 1.0

        # Overall quality score
        overall_quality = (confidence_consistency + severity_alignment) / 2

        return {
            "overall_quality": overall_quality,
            "confidence_consistency": confidence_consistency,
            "severity_alignment": severity_alignment
        }

    def filter_by_confidence(self, detections: List[DamageDetection]) -> List[DamageDetection]:
        """Filter detections by confidence threshold with quality evaluation"""
        # Evaluate response quality
        quality_metrics = self.evaluate_response_quality(detections)

        # Adjust confidence threshold based on quality
        adjusted_threshold = self.confidence_threshold
        if quality_metrics["overall_quality"] < 0.6:
            adjusted_threshold = min(0.6, self.confidence_threshold + 0.1)  # Be more strict
            print(f"⚠️  Low quality response detected. Adjusting threshold to {adjusted_threshold:.1%}")

        filtered = [d for d in detections if d.confidence >= adjusted_threshold]

        # Log filtering results
        total_detections = len(detections)
        filtered_count = len(filtered)
        removed_count = total_detections - filtered_count

        print(f"🎯 LLM Analysis Results:")
        print(f"   Total detections: {total_detections}")
        print(f"   Quality score: {quality_metrics['overall_quality']:.1%}")
        print(f"   Confidence threshold: {adjusted_threshold:.1%}")
        print(f"   High confidence detections: {filtered_count}")
        print(f"   Filtered out: {removed_count}")

        for detection in filtered:
            confidence_icon = "🔴" if detection.confidence < 0.5 else "🟡" if detection.confidence < 0.7 else "🟢"
            print(f"   ✅ {detection.part_name}: {confidence_icon} {detection.confidence:.1%} - {detection.severity}")

        for detection in detections:
            if detection.confidence < adjusted_threshold:
                print(f"   🚫 {detection.part_name}: {detection.confidence:.1%} - FILTERED OUT")

        return filtered
    
    def create_detection_visualization(self, image_path: str, detections: List[DamageDetection]) -> Optional[np.ndarray]:
        """Create visualization image with damage annotations"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                return None
            
            # Get image dimensions
            height, width = image.shape[:2]
            
            # Colors for different severity levels
            severity_colors = {
                'minor': (0, 255, 255),    # Yellow
                'moderate': (0, 165, 255), # Orange
                'severe': (0, 0, 255)      # Red
            }
            
            # Add text annotations for detected damage
            y_offset = 30
            for i, detection in enumerate(detections):
                if detection.confidence >= self.confidence_threshold:
                    color = severity_colors.get(detection.severity, (0, 255, 0))
                    
                    # Create damage label
                    label = f"{detection.part_name}: {detection.severity} ({detection.confidence:.1%})"
                    
                    # Add background rectangle for text
                    text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                    cv2.rectangle(image, (10, y_offset - 25), 
                                (10 + text_size[0] + 10, y_offset + 5), color, -1)
                    
                    # Add text
                    cv2.putText(image, label, (15, y_offset), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                    
                    y_offset += 40
            
            # Add summary at the bottom
            if detections:
                summary = f"Total Damage Detected: {len(detections)} parts"
                text_size = cv2.getTextSize(summary, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
                cv2.rectangle(image, (10, height - 40), 
                            (10 + text_size[0] + 10, height - 10), (0, 0, 0), -1)
                cv2.putText(image, summary, (15, height - 20), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            
            return image
            
        except Exception as e:
            print(f"Error creating visualization: {e}")
            return None
    
    def get_part_counts_for_pricing(self, detections: List[DamageDetection]) -> Dict[str, int]:
        """Convert detections to part counts for pricing system compatibility"""
        part_counts = {}
        
        # Map LLM part names to class IDs (for compatibility with existing pricing)
        part_name_to_id = {
            'Bonnet': 0, 'Bumper': 1, 'Dickey': 2, 'Door': 3, 
            'Fender': 4, 'Light': 5, 'Windshield': 6
        }
        
        for detection in detections:
            if detection.confidence >= self.confidence_threshold and detection.repair_needed:
                class_id = part_name_to_id.get(detection.part_name)
                if class_id is not None:
                    part_counts[class_id] = part_counts.get(class_id, 0) + 1
        
        return part_counts


def get_part_name_from_id(class_id: int) -> str:
    """Get part name from class ID (for compatibility)"""
    class_names = ['Bonnet', 'Bumper', 'Dickey', 'Door', 'Fender', 'Light', 'Windshield']
    if 0 <= class_id < len(class_names):
        return class_names[int(class_id)]
    return None
