{% extends "base.html" %}

{% block title %}Sign In - VehicleCare Pro{% endblock %}

{% block extra_css %}
<style>
    .login-container {
        max-width: 450px;
        margin: 3rem auto;
    }

    .login-card {
        background: var(--bg-primary);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        border: 1px solid var(--border-color);
    }

    .login-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 2.5rem 2rem;
        text-align: center;
    }

    .login-title {
        font-size: 2.25rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .login-subtitle {
        opacity: 0.9;
        font-size: 1rem;
    }

    .login-form {
        padding: 2.5rem 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.875rem;
    }

    .input-wrapper {
        position: relative;
    }

    .input-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        font-size: 1rem;
    }

    .form-input {
        width: 100%;
        padding: 1rem 1rem 1rem 3rem;
        border: 2px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: all 0.2s ease;
        background: var(--bg-primary);
        color: var(--text-primary);
        box-sizing: border-box;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        transform: translateY(-1px);
    }

    .form-input:hover {
        border-color: var(--primary-color);
    }

    .form-input::placeholder {
        color: var(--text-muted);
    }

    .submit-btn {
        width: 100%;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1.5rem;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .submit-btn:active {
        transform: translateY(0);
    }

    .form-footer {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-color);
    }

    .signup-link {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .signup-link a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.2s ease;
    }

    .signup-link a:hover {
        color: var(--primary-dark);
        text-decoration: underline;
    }

    .forgot-password {
        text-align: center;
        margin-top: 1rem;
    }

    .forgot-password a {
        color: var(--text-muted);
        text-decoration: none;
        font-size: 0.875rem;
        transition: color 0.2s ease;
    }

    .forgot-password a:hover {
        color: var(--primary-color);
        text-decoration: underline;
    }

    .features-preview {
        background: var(--bg-tertiary);
        padding: 1.5rem;
        border-radius: var(--border-radius);
        margin-bottom: 1.5rem;
    }

    .features-preview h3 {
        margin: 0 0 1rem 0;
        color: var(--text-primary);
        font-size: 1rem;
        font-weight: 600;
        text-align: center;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .feature-list li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .feature-list li i {
        color: var(--success-color);
        font-size: 0.75rem;
    }

    @media (max-width: 768px) {
        .login-container {
            margin: 1rem;
        }
        
        .login-form {
            padding: 2rem 1.5rem;
        }
        
        .login-header {
            padding: 2rem 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h1 class="login-title">Welcome Back</h1>
            <p class="login-subtitle">Sign in to access your VehicleCare Pro dashboard</p>
        </div>
        
        <div class="login-form">
            <div class="features-preview">
                <h3>What you can do:</h3>
                <ul class="feature-list">
                    <li><i class="fas fa-check-circle"></i> Upload vehicle damage photos</li>
                    <li><i class="fas fa-check-circle"></i> Get instant AI damage analysis</li>
                    <li><i class="fas fa-check-circle"></i> Receive accurate cost estimates</li>
                    <li><i class="fas fa-check-circle"></i> Generate professional reports</li>
                </ul>
            </div>
            
            <form action="{{ url_for('login') }}" method="POST">
                <div class="form-group">
                    <label for="email" class="form-label">Email Address</label>
                    <div class="input-wrapper">
                        <i class="fas fa-envelope input-icon"></i>
                        <input type="email" id="email" name="email" class="form-input" 
                               placeholder="Enter your email address" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-wrapper">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="password" name="password" class="form-input" 
                               placeholder="Enter your password" required>
                    </div>
                </div>
                
                <button type="submit" class="submit-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Sign In
                </button>
            </form>
            
            <div class="forgot-password">
                <a href="#">Forgot your password?</a>
            </div>
            
            <div class="form-footer">
                <div class="signup-link">
                    Don't have an account? <a href="{{ url_for('signup') }}">Create one now</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
