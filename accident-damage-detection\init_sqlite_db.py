#!/usr/bin/env python3
"""
SQLite database initialization script for Vehicle Damage Detection application.
This script creates the SQLite database, tables, and inserts sample data.
"""

import sqlite3
import json
import os

def init_database():
    """Initialize SQLite database with schema and sample data."""
    
    db_path = os.path.join(os.path.dirname(__file__), 'vehicle_damage.db')
    
    try:
        # Connect to SQLite database (creates file if it doesn't exist)
        connection = sqlite3.connect(db_path)
        cursor = connection.cursor()
        
        print(f"Creating SQLite database at: {db_path}")
        
        # Create user_info table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_info (
                user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                password TEXT NOT NULL,
                email TEXT NOT NULL UNIQUE,
                vehicle_id TEXT NOT NULL UNIQUE,
                contact_number TEXT NOT NULL,
                address TEXT NOT NULL,
                car_brand TEXT NOT NULL,
                model TEXT NOT NULL,
                registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create car_models table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS car_models (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                brand TEXT NOT NULL,
                model TEXT NOT NULL,
                part TEXT NOT NULL,
                price INTEGER NOT NULL
            )
        ''')
        
        print("✅ Tables created successfully!")
        
        # Check if car_models table has data
        cursor.execute("SELECT COUNT(*) FROM car_models")
        count = cursor.fetchone()[0]
        
        if count == 0:
            # Insert sample data from JSON file
            json_file = os.path.join(os.path.dirname(__file__), 'car_parts_prices.json')
            if os.path.exists(json_file):
                with open(json_file, 'r') as file:
                    car_parts_prices = json.load(file)
                
                for brand, models in car_parts_prices.items():
                    for model, parts in models.items():
                        for part, price in parts.items():
                            cursor.execute(
                                "INSERT INTO car_models (brand, model, part, price) VALUES (?, ?, ?, ?)",
                                (brand, model, part, price)
                            )
                
                connection.commit()
                print("✅ Sample car parts data inserted successfully!")
            else:
                print("⚠️ car_parts_prices.json file not found. Skipping sample data insertion.")
        else:
            print(f"ℹ️ Car models table already has {count} records.")
        
        # Display some statistics
        cursor.execute("SELECT COUNT(*) FROM user_info")
        user_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM car_models")
        parts_count = cursor.fetchone()[0]
        
        print(f"\n📊 Database Statistics:")
        print(f"   Users: {user_count}")
        print(f"   Car parts: {parts_count}")
        
        connection.close()
        print(f"\n✅ SQLite database initialization completed successfully!")
        print(f"   Database file: {db_path}")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ SQLite Error: {e}")
        return False
    except FileNotFoundError:
        print("❌ Error: car_parts_prices.json file not found!")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚗 Initializing Vehicle Damage Detection SQLite Database...")
    print("=" * 60)
    success = init_database()
    if success:
        print("\n🎉 Database is ready for use!")
        print("You can now run the application with: python app.py")
    else:
        print("\n💥 Database initialization failed!")
        print("Please check the error messages above and try again.")
