from flask import Flask, render_template, request, redirect, url_for, session, flash
import sqlite3
from werkzeug.utils import secure_filename
import os
import bcrypt
from collections import Counter
from dotenv import load_dotenv
from llm_damage_detector import LLMDamageDetector, get_part_name_from_id



load_dotenv()


app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY')

# Configuration
CONFIDENCE_THRESHOLD = 0.4  # Minimum confidence for damage detection (40% - balanced)


def connect_to_db():
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'vehicle_damage.db')
        print(f"Connecting to SQLite database: {db_path}")

        connection = sqlite3.connect(db_path)
        connection.row_factory = sqlite3.Row  # This allows dict-like access to rows
        print("✅ Connected successfully to SQLite database!")
        return connection
    except sqlite3.Error as e:
        print(f"❌ SQLite Error connecting to database: {e}")
        return None
    except Exception as e:
        print(f"❌ General Error connecting to database: {e}")
        return None


@app.route('/')
def home():
    return render_template('index.html')

@app.route('/test-post', methods=['GET', 'POST'])
def test_post():
    if request.method == 'POST':
        return "POST request received successfully!"
    return "GET request received. Try POST method."


@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if request.method == 'POST':
        name = request.form.get('name')
        password = request.form.get('password')
        email = request.form.get('email')
        vehicle_id = request.form.get('vehicleId')
        contact_number = request.form.get('phoneNumber')
        address = request.form.get('address')
        car_brand = request.form.get('carBrand')
        model = request.form.get('carModel')
        
        print("DATA from form")
        print(f"name : {name}")
        print(f"email : {email}")
        print(f"password : {password}")
        print(f"vehicle_id : {vehicle_id}")
        print(f"contact_number : {contact_number}")
        print(f"address : {address}")
        print(f"car_brand : {car_brand}")
        print(f"model : {model}")

        if not all([name, password, email, vehicle_id, contact_number, address, car_brand, model]):
            flash("All fields are required!", "error")
            print("All fields are required!")
            return render_template('signup.html')

        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        print(f"hashed_password : {hashed_password}")

        connection = connect_to_db()
        if connection:
            try:
                cursor = connection.cursor()
                cursor.execute('''
                    INSERT INTO user_info (name, password, email, vehicle_id, contact_number, address, car_brand, model)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (name, hashed_password, email, vehicle_id, contact_number, address, car_brand, model))
                connection.commit()
                connection.close()

                flash("Signup successful!", "success")
                session['user_email'] = email
                return redirect(url_for('dashboard'))
            except sqlite3.IntegrityError as e:
                connection.close()
                if 'UNIQUE constraint failed' in str(e):
                    flash("Email already exists. Please use a different email.", "error")
                else:
                    flash("An error occurred while signing up. Please try again.", "error")
            except sqlite3.Error as e:
                connection.close()
                print(f"Error executing query: {e}")
                flash("An error occurred while signing up. Please try again.", "error")
        else:
            flash("Database connection failed. Please try again later.", "error")
            
            
    return render_template('signup.html')


@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        print(f"Email : {email}")
        print(f"Password : {password}")

        if not email or not password:
            flash("Email and password are required!", "error")
            return render_template('login.html')

        connection = connect_to_db()
        if connection:
            try:
                cursor = connection.cursor()
                cursor.execute("SELECT password FROM user_info WHERE email = ?", (email,))
                result = cursor.fetchone()
                connection.close()

                if result and bcrypt.checkpw(password.encode('utf-8'), result[0].encode('utf-8')):
                    session['user_email'] = email
                    flash("Login successful!", "success")
                    return redirect(url_for('dashboard'))
                else:
                    flash("Invalid email or password.", "error")
            except sqlite3.Error as e:
                connection.close()
                print(f"Error executing query: {e}")
                flash("An error occurred during login. Please try again.", "error")
        else:
            flash("Database connection failed. Please try again later.", "error")

    return render_template('login.html')


@app.route('/logout')
def logout():
    session.pop('user_email', None)  # Remove user email from session
    flash("You have been logged out.", "info")
    
    return redirect(url_for('login'))


# LLM damage detector will be loaded lazily
llm_detector = None

def load_llm_detector():
    """Load LLM damage detector lazily when needed"""
    global llm_detector
    if llm_detector is None:
        print("Loading LLM damage detector...")
        try:
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                print("❌ OpenAI API key not found. Please set OPENAI_API_KEY environment variable.")
                return None

            llm_detector = LLMDamageDetector(api_key=api_key)
            print("✅ LLM damage detector loaded successfully!")
        except Exception as e:
            print(f"❌ Error loading LLM damage detector: {e}")
            return None
    return llm_detector


@app.route('/dashboard', methods=['GET', 'POST'])
def dashboard():
    if request.method == 'POST':
        file = request.files.get('file')  # Changed from 'image' to 'file'
        if not file or file.filename == '':
            flash('Please upload an image.', 'error')
            return render_template('dashboard.html')

        filename = secure_filename(file.filename)
        if not filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            flash('Invalid file type. Please upload an image.', 'error')
            return render_template('dashboard.html')
        
        # Create static directory if it doesn't exist
        static_dir = os.path.join(os.path.dirname(__file__), 'static')
        os.makedirs(static_dir, exist_ok=True)

        # Save the uploaded image
        image_path = os.path.join(static_dir, 'uploaded_image.jpg')
        print("File uploaded successfully")

        file.save(image_path)
        # print(f"Upload image path : {image_path}")

        # Load LLM detector if not already loaded
        detector = load_llm_detector()
        if detector is None:
            flash('LLM damage detector not available. Please check API configuration.', 'error')
            return render_template('dashboard.html')

        # Analyze image using LLM
        print("🤖 Analyzing image with LLM...")
        detections = detector.analyze_image(image_path)

        # Filter detections by confidence threshold
        filtered_detections = detector.filter_by_confidence(detections)

        # Convert to class counts for compatibility with pricing system
        class_counts = detector.get_part_counts_for_pricing(filtered_detections)

        print(f"🎯 LLM Analysis complete: {len(filtered_detections)} parts detected")
        for detection in filtered_detections:
            print(f"   ✅ {detection.part_name}: {detection.confidence:.1%} - {detection.severity}")

        # Create detection visualization image
        detected_image_path = os.path.join(static_dir, 'detected_image.jpg')
        detection_image = detector.create_detection_visualization(image_path, filtered_detections)

        if detection_image is not None:
            import cv2
            cv2.imwrite(detected_image_path, detection_image)
            print(f"✅ Detection visualization saved: {detected_image_path}")
        else:
            print("❌ Failed to create detection visualization")
        # Get the user's email from session
        user_email = session.get('user_email')
        print(user_email)
        if not user_email:
            flash('You need to log in to get an estimate.', 'error')
            return redirect(url_for('login'))

        # Fetch part prices from the database
        part_prices = get_part_prices(user_email, class_counts)

        # Calculate total cost
        total_cost = sum(item['total'] for item in part_prices.values()) if part_prices else 0

        # Prepare damage info for display
        damage_info = {}
        for class_id, count in class_counts.items():
            part_name = get_part_name_from_id(class_id)
            if part_name:
                damage_info[part_name] = count

        print(f"🎯 LLM Analysis complete: {len(damage_info)} parts detected, Total cost: ₹{total_cost:,}")

        return render_template('dashboard.html',
                             uploaded_image_path='uploaded_image.jpg',
                             detected_image_path='detected_image.jpg',
                             damage_info=damage_info,
                             prices=part_prices,
                             total_cost=total_cost)

    return render_template('dashboard.html')


@app.route('/estimate')
def estimate():
    """Route to show the estimate page with the last analysis results"""
    if 'user_email' not in session:
        flash('You need to login to view estimates.', 'error')
        return redirect(url_for('login'))

    # Check if we have recent analysis results
    static_dir = os.path.join(os.path.dirname(__file__), 'static')
    uploaded_image = os.path.join(static_dir, 'uploaded_image.jpg')
    detected_image = os.path.join(static_dir, 'detected_image.jpg')

    if not (os.path.exists(uploaded_image) and os.path.exists(detected_image)):
        flash('No recent analysis found. Please upload an image first.', 'error')
        return redirect(url_for('dashboard'))

    # Re-analyze the uploaded image to get pricing data
    try:
        # Load LLM detector and analyze the uploaded image
        detector = load_llm_detector()
        if detector is None:
            flash('LLM damage detector not available.', 'error')
            return redirect(url_for('dashboard'))

        # Analyze image using LLM
        detections = detector.analyze_image(uploaded_image)

        # Filter detections by confidence threshold
        filtered_detections = detector.filter_by_confidence(detections)

        # Convert to class counts for compatibility with pricing system
        class_counts = detector.get_part_counts_for_pricing(filtered_detections)

        # Get pricing data
        user_email = session.get('user_email')
        part_prices = get_part_prices(user_email, class_counts)

        return render_template('estimate.html',
                             original_image='uploaded_image.jpg',
                             detected_image='detected_image.jpg',
                             part_prices=part_prices)

    except Exception as e:
        print(f"Error in estimate route: {e}")
        flash('Error generating estimate. Please try uploading the image again.', 'error')
        return redirect(url_for('dashboard'))


def get_part_prices(email, class_counts):
    print(f"🔍 Getting part prices for email: {email}")
    print(f"🔍 Class counts: {class_counts}")

    connection = connect_to_db()
    if connection:
        try:
            cursor = connection.cursor()
            # Get user's car brand and model
            cursor.execute("SELECT car_brand, model FROM user_info WHERE email = ?", (email,))
            user_data = cursor.fetchone()
            if not user_data:
                print("❌ User not found in database")
                connection.close()
                return {}

            car_brand = user_data['car_brand']
            car_model = user_data['model']
            print(f"✅ User found - Brand: {car_brand}, Model: {car_model}")

            # Normalize brand and model names for better matching
            normalized_brand = normalize_brand_name(car_brand)
            normalized_model = normalize_model_name(car_model)
            print(f"🔄 Normalized - Brand: {normalized_brand}, Model: {normalized_model}")

            # Fetch part prices
            prices = {}
            for class_id, count in class_counts.items():
                part_name = get_part_name_from_id(class_id)
                print(f"🔍 Processing class_id {class_id} -> part_name: {part_name}, count: {count}")

                if part_name:
                    # Try exact match first
                    cursor.execute(
                        "SELECT price FROM car_models WHERE UPPER(brand) = UPPER(?) AND UPPER(model) = UPPER(?) AND UPPER(part) = UPPER(?)",
                        (normalized_brand, normalized_model, part_name)
                    )
                    price_data = cursor.fetchone()
                    print(f"🔍 Price query result for {part_name}: {price_data}")

                    if price_data:
                        price_per_part = price_data['price']
                        total_price = price_per_part * count
                        prices[part_name] = {'count': count, 'price': price_per_part, 'total': total_price}
                        print(f"✅ Added {part_name}: count={count}, price={price_per_part}, total={total_price}")
                    else:
                        print(f"❌ No price found for {normalized_brand} {normalized_model} {part_name}")

            print(f"🎯 Final prices dictionary: {prices}")
            connection.close()
            return prices
        except sqlite3.Error as e:
            connection.close()
            print(f"❌ SQLite Error executing query: {e}")
            return {}
    print("❌ Database connection failed")
    return {}


def normalize_brand_name(brand):
    """Normalize brand names to match database entries"""
    brand_mapping = {
        'MARUTHI': 'MARUTI SUZUKI',
        'MARUTI': 'MARUTI SUZUKI',
        'SUZUKI': 'MARUTI SUZUKI',
        'HONDA': 'HONDA',
        'TOYOTA': 'TOYOTA',
        'HYUNDAI': 'HYUNDAI',
        'NISSAN': 'NISSAN',
        'SKODA': 'SKODA'
    }

    brand_upper = brand.upper().strip()
    return brand_mapping.get(brand_upper, brand_upper)

def normalize_model_name(model):
    """Normalize model names to match database entries"""
    # Convert to title case for better matching
    return model.strip().title()

def get_part_name_from_id(class_id):
    class_names = ['Bonnet', 'Bumper', 'Dickey', 'Door', 'Fender', 'Light', 'Windshield']
    if 0 <= class_id < len(class_names):
        return class_names[int(class_id)]
    return None

# Note: Detection visualization is now handled by LLMDamageDetector.create_detection_visualization()


@app.route('/view_profile')
def view_profile():
    if 'user_email' not in session:
        flash('You need to login to view your profile.', 'error')
        return redirect(url_for('login'))

    connection = connect_to_db()
    if connection:
        try:
            cursor = connection.cursor()
            # Fetch current user information
            cursor.execute("SELECT * FROM user_info WHERE email = ?", (session['user_email'],))
            user_info = cursor.fetchone()
            connection.close()

            if not user_info:
                flash('User not found.', 'error')
                return redirect(url_for('dashboard'))
            return render_template('view_profile.html', user_info=user_info)
        except sqlite3.Error as e:
            connection.close()
            print(f"Error executing query: {e}")
            flash("An error occurred while fetching your profile. Please try again.", "error")
    else:
        flash("Database connection failed. Please try again later.", "error")

    return redirect(url_for('dashboard'))


@app.route('/edit_profile', methods=['GET', 'POST'])
def edit_profile():
    if 'user_email' not in session:
        flash('You need to login to edit your profile.', 'error')
        return redirect(url_for('login'))

    connection = connect_to_db()
    if connection:
        try:
            cursor = connection.cursor()
            if request.method == 'POST':
                # Update user information
                cursor.execute('''
                    UPDATE user_info
                    SET name = ?, email = ?, vehicle_id = ?, contact_number = ?,
                        address = ?, car_brand = ?, model = ?
                    WHERE email = ?
                ''', (
                    request.form['name'],
                    request.form['email'],
                    request.form['vehicleId'],
                    request.form['phoneNumber'],
                    request.form['address'],
                    request.form['carBrand'],
                    request.form['carModel'],
                    session['user_email']
                ))
                connection.commit()
                connection.close()
                flash('Profile updated successfully!', 'success')
                session['user_email'] = request.form['email']  # Update session if email changed
                return redirect(url_for('dashboard'))

            # Fetch current user information
            cursor.execute("SELECT * FROM user_info WHERE email = ?", (session['user_email'],))
            user_info = cursor.fetchone()
            connection.close()
            return render_template('edit_profile.html', user_info=user_info)

        except sqlite3.Error as e:
            connection.close()
            print(f"Error executing query: {e}")
            flash("An error occurred while updating your profile. Please try again.", "error")
    else:
        flash("Database connection failed. Please try again later.", "error")

    return redirect(url_for('dashboard'))

if __name__ == '__main__':
    print("🚗 Starting LLM-Powered Vehicle Damage Detection Application...")
    print("📍 Application will be available at: http://127.0.0.1:8080")
    print("🤖 AI Engine: GPT-4V (Vision Language Model)")
    print("🔑 API Key configured:", "✅" if os.getenv('OPENAI_API_KEY') else "❌")
    print("🗄️ Database: SQLite (vehicle_damage.db)")
    print("🔧 Debug mode: ON")
    print("=" * 50)
    app.run(debug=True, host='127.0.0.1', port=8080)