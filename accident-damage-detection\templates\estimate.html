<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vehicle Damage Assessment Report</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: white;
            color: #333;
            line-height: 1.4;
        }

        .report-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }

        .report-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }

        .report-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .report-subtitle {
            font-size: 14px;
            color: #666;
        }

        .images-section {
            margin-bottom: 30px;
        }

        .images-container {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }

        .image-box {
            flex: 1;
            text-align: center;
        }

        .image-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .image-box img {
            width: 100%;
            max-width: 350px;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .damage-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .damage-table th,
        .damage-table td {
            border: 1px solid #333;
            padding: 12px 8px;
            text-align: center;
        }

        .damage-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            color: #333;
        }

        .damage-table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .total-row {
            background-color: #e8f4f8 !important;
            font-weight: bold;
            font-size: 16px;
        }

        .total-amount {
            font-size: 18px;
            font-weight: bold;
            color: #2c5aa0;
        }

        .print-button {
            display: block;
            width: 150px;
            margin: 30px auto;
            padding: 12px;
            background-color: #2c5aa0;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            font-weight: bold;
        }

        .print-button:hover {
            background-color: #1e3d6f;
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
            }

            .report-container {
                max-width: none;
                margin: 0;
                padding: 15px;
            }

            .print-button {
                display: none;
            }

            .images-container {
                flex-direction: column;
                gap: 15px;
            }

            .image-box img {
                max-width: 300px;
            }
        }

        @media (max-width: 768px) {
            .images-container {
                flex-direction: column;
            }

            .report-container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- Report Header -->
        <div class="report-header">
            <div class="report-title">Vehicle Damage Assessment Report</div>
            <div class="report-subtitle">AI-Powered Damage Analysis & Cost Estimation</div>
        </div>

        <!-- Images Section -->
        <div class="images-section">
            <div class="images-container">
                <div class="image-box">
                    <div class="image-title">Original Image</div>
                    <img src="{{ url_for('static', filename=original_image) }}" alt="Original Vehicle Image">
                </div>
                <div class="image-box">
                    <div class="image-title">Damage Analysis</div>
                    <img src="{{ url_for('static', filename=detected_image) }}" alt="Analyzed Damage Image">
                </div>
            </div>
        </div>

        <!-- Damage Assessment Table -->
        <table class="damage-table">
            <thead>
                <tr>
                    <th>Damaged Part</th>
                    <th>Quantity</th>
                    <th>Unit Price (₹)</th>
                    <th>Total Cost (₹)</th>
                </tr>
            </thead>
            <tbody>
                {% for part, details in part_prices.items() %}
                <tr>
                    <td>{{ part }}</td>
                    <td>{{ details['count'] }}</td>
                    <td>₹{{ "{:,}".format(details['price']) }}</td>
                    <td>₹{{ "{:,}".format(details['total']) }}</td>
                </tr>
                {% endfor %}
                <tr class="total-row">
                    <td colspan="3"><strong>TOTAL ESTIMATED REPAIR COST</strong></td>
                    <td class="total-amount">₹{{ "{:,}".format(part_prices.values() | map(attribute='total') | sum) }}</td>
                </tr>
            </tbody>
        </table>

        <!-- Print Button (hidden when printing) -->
        <button class="print-button" onclick="window.print()">Print Report</button>
    </div>
</body>
</html>