#!/usr/bin/env python3
"""
Start the app with the simple working detector
"""

import os
import sys

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded")
except ImportError:
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Environment variables loaded manually")
    except Exception as e:
        print(f"⚠️ Could not load .env file: {e}")

def test_simple_detector():
    """Test the simple detector first"""
    
    print("🔧 TESTING SIMPLE WORKING DETECTOR")
    print("=" * 60)
    
    try:
        from simple_working_detector import SimpleWorkingDetector
        
        detector = SimpleWorkingDetector()
        
        # Test with uploaded image if available
        test_image = "static/uploaded_image.jpg"
        if os.path.exists(test_image):
            print(f"🔍 Testing with: {test_image}")
            results = detector.analyze_image(test_image)
            
            if results:
                print(f"✅ WORKING: {len(results)} damage areas detected")
                for result in results:
                    print(f"   • {result.part_name}: {result.severity} ({result.confidence:.1%})")
                return True
            else:
                print("⚠️ No damage detected (may be clean image)")
                return True  # Still working, just no damage
        else:
            print("⚠️ No test image available")
            return True  # Detector loads fine
            
    except Exception as e:
        print(f"❌ Simple detector test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_app():
    """Start the Flask app"""
    
    print("\n🚀 STARTING APP WITH SIMPLE WORKING DETECTOR")
    print("=" * 70)
    
    try:
        from app import app
        
        print("✅ App loaded with Simple Working Detector!")
        print("\n🔧 Simple Working Detector Features:")
        print("   • Direct, reliable damage detection")
        print("   • Automatic door/bumper confusion fixes")
        print("   • Handles both Gemini and OpenAI")
        print("   • JSON parsing with markdown handling")
        print("   • Simple validation (not overly strict)")
        
        print("\n📍 URL: http://127.0.0.1:8080")
        print("🔑 Login: <EMAIL> / demo123")
        print("\n🎯 Expected behavior:")
        print("   • Real damage: DETECTED (not 'no significant damage')")
        print("   • Door damage: Correctly identified as 'Door'")
        print("   • Bumper damage: Correctly identified as 'Bumper'")
        print("   • Clean images: No false positives")
        
        print("\n💡 Testing your issues:")
        print("   1. Upload image with door damage")
        print("      → Should show 'Door' damage, NOT 'Bumper'")
        print("   2. Upload image with real damage")
        print("      → Should detect damage, NOT 'no significant damage'")
        print("   3. Console: Look for 'Simple analysis' messages")
        
        print("\n🔧 Console messages to look for:")
        print("   • 'Simple analysis starting: ...'")
        print("   • 'Gemini response: ...' or 'OpenAI response: ...'")
        print("   • 'FIXED: ... → ...' (for part name corrections)")
        print("   • 'Simple analysis complete: X damage areas found'")
        
        print("\n" + "=" * 70)
        print("🎉 STARTING SIMPLE WORKING DAMAGE DETECTION")
        print("=" * 70)
        
        app.run(debug=True, host='127.0.0.1', port=8080)
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped")
    except Exception as e:
        print(f"❌ App start failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔧 SIMPLE WORKING APP STARTER")
    print("=" * 80)
    
    # Test the detector first
    if test_simple_detector():
        print("\n🎉 SIMPLE DETECTOR WORKING!")
        print("✅ Ready to start app")
        
        # Start the app
        start_app()
    else:
        print("\n❌ SIMPLE DETECTOR ISSUES")
        print("🔧 Please check:")
        print("   1. Environment variables loaded correctly")
        print("   2. API keys configured")
        print("   3. Dependencies installed")
        
        sys.exit(1)
