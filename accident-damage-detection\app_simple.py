from flask import Flask, render_template, request, redirect, url_for, session, flash
import os

app = Flask(__name__)
app.secret_key = 'test_secret_key_for_development'

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/test-post', methods=['GET', 'POST'])
def test_post():
    if request.method == 'POST':
        return "POST request received successfully!"
    return "GET request received. Try POST method."

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if request.method == 'POST':
        name = request.form.get('name')
        password = request.form.get('password')
        email = request.form.get('email')
        vehicle_id = request.form.get('vehicleId')
        contact_number = request.form.get('phoneNumber')
        address = request.form.get('address')
        car_brand = request.form.get('carBrand')
        model = request.form.get('carModel')
        
        print("=== SIGNUP FORM DATA ===")
        print(f"name: {name}")
        print(f"email: {email}")
        print(f"password: {password}")
        print(f"vehicle_id: {vehicle_id}")
        print(f"contact_number: {contact_number}")
        print(f"address: {address}")
        print(f"car_brand: {car_brand}")
        print(f"model: {model}")
        print("========================")

        if not all([name, password, email, vehicle_id, contact_number, address, car_brand, model]):
            flash("All fields are required!", "error")
            print("❌ All fields are required!")
            return render_template('signup.html')

        # For now, just simulate successful signup without database
        flash("Signup successful! (Database connection will be added later)", "success")
        print("✅ Signup successful!")
        return redirect(url_for('dashboard'))
            
    return render_template('signup.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        print(f"Login attempt - Email: {email}, Password: {password}")

        if not email or not password:
            flash("Email and password are required!", "error")
            return render_template('login.html')

        # For now, accept any login
        session['user_email'] = email
        flash("Login successful!", "success")
        return redirect(url_for('dashboard'))

    return render_template('login.html')

@app.route('/dashboard')
def dashboard():
    if 'user_email' not in session:
        flash('You need to login to access the dashboard.', 'error')
        return redirect(url_for('login'))
    
    return render_template('dashboard.html')

@app.route('/logout')
def logout():
    session.pop('user_email', None)
    flash("You have been logged out.", "info")
    return redirect(url_for('login'))

if __name__ == '__main__':
    print("🚗 Starting Simple Vehicle Damage Detection Application...")
    print("📍 Application will be available at: http://127.0.0.1:5001")
    print("🔧 Debug mode: ON")
    print("📝 This is a simplified version for testing")
    print("=" * 50)
    app.run(debug=True, host='127.0.0.1', port=5001)
