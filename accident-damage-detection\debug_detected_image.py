#!/usr/bin/env python3
"""
Debug detected image creation issue
"""

import os
import time
import cv2
import numpy as np
from llm_damage_detector import LLMDamageDetector

def debug_detected_image():
    """Debug why detected image shows 'not available'"""
    
    print("🔍 DEBUGGING DETECTED IMAGE ISSUE")
    print("=" * 50)
    
    static_dir = os.path.join(os.path.dirname(__file__), 'static')
    os.makedirs(static_dir, exist_ok=True)
    
    uploaded_path = os.path.join(static_dir, 'uploaded_image.jpg')
    detected_path = os.path.join(static_dir, 'detected_image.jpg')
    
    # Step 1: Create a test uploaded image
    print("📸 Step 1: Creating test uploaded image...")
    test_img = np.ones((600, 800, 3), dtype=np.uint8) * 180
    cv2.putText(test_img, "TEST VEHICLE IMAGE", (200, 300), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 2)
    cv2.rectangle(test_img, (100, 400), (700, 500), (0, 0, 255), 3)  # Red damage area
    cv2.imwrite(uploaded_path, test_img)
    
    if os.path.exists(uploaded_path):
        size = os.path.getsize(uploaded_path)
        print(f"✅ Uploaded image created: {size:,} bytes")
    else:
        print("❌ Failed to create uploaded image")
        return False
    
    # Step 2: Initialize detector
    print("\n🤖 Step 2: Initializing LLM detector...")
    try:
        detector = LLMDamageDetector()
        print("✅ LLM detector initialized")
    except Exception as e:
        print(f"❌ Failed to initialize detector: {e}")
        return False
    
    # Step 3: Get detections
    print("\n📋 Step 3: Getting detections...")
    try:
        detections = detector._get_demo_detections()
        print(f"✅ Generated {len(detections)} demo detections")
        for detection in detections:
            print(f"   • {detection.part_name}: {detection.severity} ({detection.confidence:.1%})")
    except Exception as e:
        print(f"❌ Failed to get detections: {e}")
        return False
    
    # Step 4: Create visualization
    print("\n🎨 Step 4: Creating visualization...")
    try:
        print(f"   Input image: {uploaded_path}")
        print(f"   Output path: {detected_path}")
        print(f"   Detections: {len(detections)}")
        
        # Remove old detected image if exists
        if os.path.exists(detected_path):
            os.remove(detected_path)
            print("   Removed old detected image")
        
        detection_image = detector.create_detection_visualization(uploaded_path, detections)
        
        if detection_image is not None:
            print("✅ Visualization created successfully")
            print(f"   Image shape: {detection_image.shape}")
            print(f"   Image type: {detection_image.dtype}")
            
            # Save the image
            success = cv2.imwrite(detected_path, detection_image)
            if success:
                print("✅ Image saved successfully")
            else:
                print("❌ Failed to save image")
                return False
        else:
            print("❌ Visualization creation returned None")
            return False
            
    except Exception as e:
        print(f"❌ Error creating visualization: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Step 5: Verify saved image
    print("\n🔍 Step 5: Verifying saved image...")
    
    if os.path.exists(detected_path):
        size = os.path.getsize(detected_path)
        print(f"✅ Detected image file exists: {size:,} bytes")
        
        # Try to read the image back
        try:
            saved_img = cv2.imread(detected_path)
            if saved_img is not None:
                h, w = saved_img.shape[:2]
                print(f"✅ Image readable: {w}x{h} pixels")
                
                # Check if image has content (not all black/white)
                mean_val = np.mean(saved_img)
                print(f"✅ Image mean value: {mean_val:.1f} (should be > 0 and < 255)")
                
                if 10 < mean_val < 245:
                    print("✅ Image appears to have proper content")
                else:
                    print("⚠️ Image might be too dark or too bright")
                
            else:
                print("❌ Saved image is not readable")
                return False
                
        except Exception as e:
            print(f"❌ Error reading saved image: {e}")
            return False
    else:
        print("❌ Detected image file does not exist")
        return False
    
    # Step 6: Test web accessibility
    print("\n🌐 Step 6: Testing web accessibility...")
    
    # Simulate web access
    web_url = f"/static/detected_image.jpg"
    print(f"   Web URL: {web_url}")
    
    # Check file permissions
    try:
        with open(detected_path, 'rb') as f:
            data = f.read(1024)  # Read first 1KB
        print(f"✅ File readable via filesystem: {len(data)} bytes read")
    except Exception as e:
        print(f"❌ File permission error: {e}")
        return False
    
    # Step 7: Compare timestamps
    print("\n⏰ Step 7: Checking timestamps...")
    
    uploaded_mtime = os.path.getmtime(uploaded_path)
    detected_mtime = os.path.getmtime(detected_path)
    current_time = time.time()
    
    print(f"   Uploaded image: {time.ctime(uploaded_mtime)}")
    print(f"   Detected image: {time.ctime(detected_mtime)}")
    print(f"   Current time: {time.ctime(current_time)}")
    
    if detected_mtime > uploaded_mtime:
        print("✅ Detected image is newer than uploaded image")
    else:
        print("⚠️ Detected image is older than uploaded image")
    
    if current_time - detected_mtime < 60:  # Within last minute
        print("✅ Detected image was created recently")
    else:
        print("⚠️ Detected image is old")
    
    return True

if __name__ == "__main__":
    success = debug_detected_image()
    
    if success:
        print("\n🎉 DETECTED IMAGE DEBUG COMPLETE!")
        print("\nThe detected image should be working properly.")
        print("If you still see 'Analysis image not available':")
        print("1. Clear browser cache completely")
        print("2. Check browser developer tools for 404 errors")
        print("3. Try refreshing the page")
        print("4. Check if Flask static file serving is working")
    else:
        print("\n❌ DETECTED IMAGE HAS ISSUES!")
        print("Check the error messages above to identify the problem")
