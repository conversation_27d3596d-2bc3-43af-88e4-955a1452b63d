#!/usr/bin/env python3
"""
Start the app with Gemini Pro Vision configuration
"""

import os
import sys

# Load environment variables first
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    # Manual .env loading
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Environment variables loaded manually")
    except Exception as e:
        print(f"⚠️ Could not load .env file: {e}")

def verify_gemini_configuration():
    """Verify Gemini is properly configured"""
    
    print("🔍 VERIFYING GEMINI CONFIGURATION")
    print("=" * 60)
    
    gemini_key = os.getenv('GEMINI_API_KEY', 'not_found')
    selected_llm = os.getenv('DAMAGE_DETECTION_LLM', 'not_found')
    
    print(f"GEMINI_API_KEY: {'✅ Configured' if gemini_key != 'not_found' else '❌ Missing'}")
    print(f"DAMAGE_DETECTION_LLM: {selected_llm}")
    
    if gemini_key == 'not_found':
        print("❌ Gemini API key not found")
        return False
    
    if selected_llm != 'gemini':
        print("❌ LLM not set to Gemini")
        return False
    
    print("✅ Gemini configuration verified")
    return True

def start_app():
    """Start the Flask app with Gemini"""
    
    print("\n🚀 STARTING APP WITH GEMINI PRO VISION")
    print("=" * 60)
    
    try:
        # Import and start the app
        from app import app
        
        print("✅ App loaded with Gemini Pro Vision!")
        print("\n🤖 Gemini Pro Vision features:")
        print("   • Google's advanced vision model")
        print("   • Better accuracy than GPT-4V")
        print("   • Lower false positive rate")
        print("   • Cost-effective pricing")
        print("   • Anti-false-positive prompt")
        
        print("\n📍 URL: http://127.0.0.1:8080")
        print("🔑 Login: <EMAIL> / demo123")
        print("\n🎯 Expected Gemini behavior:")
        print("   • Clean vehicles: NO false bumper damage")
        print("   • Better accuracy than OpenAI")
        print("   • More conservative damage detection")
        print("   • Specific damage descriptions")
        
        print("\n💡 Testing your bumper issue:")
        print("   • Upload your original image")
        print("   • Should show NO false bumper damage")
        print("   • Console: 'GEMINI analysis complete: X damage areas'")
        print("   • Better accuracy expected")
        
        print("\n🔧 Console messages to look for:")
        print("   • 'Using GEMINI for damage analysis...'")
        print("   • 'Gemini Pro Vision response: []' (for clean images)")
        print("   • 'GEMINI analysis complete: 0 damage areas found'")
        
        print("\n" + "=" * 60)
        print("🎉 STARTING GEMINI-POWERED DAMAGE DETECTION")
        print("=" * 60)
        
        app.run(debug=True, host='127.0.0.1', port=8080)
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped")
    except Exception as e:
        print(f"❌ App start failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🤖 GEMINI PRO VISION APP STARTER")
    print("=" * 80)
    
    # Verify configuration
    if verify_gemini_configuration():
        print("\n🎉 GEMINI READY!")
        print("✅ Configuration verified")
        print("✅ API key configured")
        print("✅ LLM set to Gemini")
        print("✅ Should eliminate bumper false positives")
        
        # Start the app
        start_app()
    else:
        print("\n❌ GEMINI CONFIGURATION ISSUES")
        print("🔧 Please check:")
        print("   1. .env file has GEMINI_API_KEY=your_key")
        print("   2. .env file has DAMAGE_DETECTION_LLM=gemini")
        print("   3. Restart terminal to reload environment")
        
        print("\n📄 Current .env content:")
        try:
            with open('.env', 'r') as f:
                for line in f:
                    if 'GEMINI' in line or 'DAMAGE_DETECTION_LLM' in line:
                        print(f"   {line.strip()}")
        except Exception as e:
            print(f"   Could not read .env: {e}")
        
        sys.exit(1)
