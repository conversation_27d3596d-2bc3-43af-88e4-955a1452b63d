#!/usr/bin/env python3
"""
Debug script to test pricing logic step by step
"""

import os
import sys
sys.path.append('.')

from app import get_part_prices, get_part_name_from_id, normalize_brand_name, normalize_model_name
from collections import Counter
import sqlite3

def debug_pricing():
    """Debug the pricing logic step by step"""
    
    print("🔍 DEBUGGING PRICING LOGIC")
    print("=" * 50)
    
    # Test user
    email = "<EMAIL>"
    
    # Simulate some damage detections
    test_cases = [
        {3: 1},  # Door: 1
        {0: 1, 1: 1},  # Bonnet: 1, Bumper: 1
        {3: 2, 4: 1},  # Door: 2, Fender: 1
    ]
    
    print(f"🧪 Testing with user: {email}")
    
    # Check user data first
    db_path = 'vehicle_damage.db'
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    cursor.execute("SELECT car_brand, model FROM user_info WHERE email = ?", (email,))
    user_data = cursor.fetchone()
    
    if user_data:
        print(f"✅ User found: {user_data['car_brand']} {user_data['model']}")
        
        # Test normalization
        normalized_brand = normalize_brand_name(user_data['car_brand'])
        normalized_model = normalize_model_name(user_data['model'])
        print(f"🔄 Normalized: {normalized_brand} {normalized_model}")
        
        # Check if parts exist in database
        cursor.execute(
            "SELECT part, price FROM car_models WHERE UPPER(brand) = UPPER(?) AND UPPER(model) = UPPER(?)",
            (normalized_brand, normalized_model)
        )
        available_parts = cursor.fetchall()
        
        print(f"\n📋 Available parts for {normalized_brand} {normalized_model}:")
        for part in available_parts:
            print(f"   {part['part']}: ₹{part['price']:,}")
        
        # Test each case
        for i, class_counts in enumerate(test_cases, 1):
            print(f"\n🧪 Test Case {i}: {class_counts}")
            
            # Convert class IDs to part names
            damage_parts = {}
            for class_id, count in class_counts.items():
                part_name = get_part_name_from_id(class_id)
                damage_parts[part_name] = count
            
            print(f"   Damage: {damage_parts}")
            
            # Get prices
            prices = get_part_prices(email, class_counts)
            
            if prices:
                total = sum(item['total'] for item in prices.values())
                print(f"   ✅ Pricing successful: ₹{total:,}")
                for part, details in prices.items():
                    print(f"      {part}: {details['count']} × ₹{details['price']:,} = ₹{details['total']:,}")
            else:
                print(f"   ❌ No pricing returned")
                
                # Debug why no pricing
                for class_id, count in class_counts.items():
                    part_name = get_part_name_from_id(class_id)
                    cursor.execute(
                        "SELECT price FROM car_models WHERE UPPER(brand) = UPPER(?) AND UPPER(model) = UPPER(?) AND UPPER(part) = UPPER(?)",
                        (normalized_brand, normalized_model, part_name)
                    )
                    price_data = cursor.fetchone()
                    
                    if price_data:
                        print(f"      ✅ {part_name} price found: ₹{price_data['price']}")
                    else:
                        print(f"      ❌ {part_name} price NOT found")
    else:
        print(f"❌ User not found: {email}")
    
    conn.close()
    
    print("\n🎯 DEBUGGING COMPLETE")
    print("If pricing is working here but not in the app,")
    print("the issue might be with template rendering or session data.")

if __name__ == "__main__":
    debug_pricing()
