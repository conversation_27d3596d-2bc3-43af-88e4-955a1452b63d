#!/usr/bin/env python3
"""
Multi-LLM Damage Detector with support for OpenAI, Claude, Gemini, and Hybrid approaches
"""

import os
import json
import base64
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import requests
from PIL import Image
import io

@dataclass
class DamageDetection:
    part_name: str
    severity: str
    confidence: float
    description: str
    repair_needed: bool
    estimated_area_percentage: float

class MultiLLMDamageDetector:
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY', 'your_openai_api_key_here')
        self.claude_api_key = os.getenv('CLAUDE_API_KEY', 'your_claude_api_key_here')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY', 'your_gemini_api_key_here')
        self.selected_llm = os.getenv('DAMAGE_DETECTION_LLM', 'openai')
        
        self.vehicle_parts = [
            "Front Bumper", "Rear Bumper", "<PERSON>", "<PERSON>", "Trunk", 
            "Fender", "Side Mirror", "Headlight", "Taillight", "Windshield",
            "Side Panel", "Roof", "Wheel", "Tire"
        ]
        
        print(f"🤖 Multi-LLM Detector initialized with: {self.selected_llm.upper()}")

    def encode_image_base64(self, image_path: str) -> str:
        """Encode image to base64 for API calls"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def create_balanced_prompt(self) -> str:
        """Create a balanced prompt that works well across different LLMs"""
        return f"""
You are an expert vehicle damage assessor. Analyze this vehicle image for damage.

BALANCED ASSESSMENT GUIDELINES:
- Report damage that is clearly visible and requires repair
- Be accurate but not overly conservative
- Distinguish between real damage and normal vehicle features
- Provide specific, detailed descriptions

Vehicle parts to analyze: {', '.join(self.vehicle_parts)}

REPORT DAMAGE if you can clearly see:
- Dents, scratches, or deformation requiring repair
- Broken, cracked, or shattered components
- Missing parts or pieces
- Paint damage exposing primer or metal
- Impact damage from accidents

DO NOT REPORT:
- Normal wear, dirt, or minor surface marks
- Shadows, reflections, or lighting effects
- Normal panel gaps or design features
- Uncertain or unclear areas

For each damaged part, provide:
1. Part name (from the list above)
2. Severity (minor/moderate/severe)
3. Confidence (0.7-1.0, only report if 0.7+)
4. Specific description of damage
5. Repair needed (true/false)
6. Estimated damage area percentage (5-100%)

Respond with JSON array:
[
  {{
    "part_name": "Door",
    "severity": "moderate",
    "confidence": 0.8,
    "description": "Visible dent on front passenger door",
    "repair_needed": true,
    "estimated_area_percentage": 20.0
  }}
]

If no damage is visible, respond with: []
"""

    def analyze_with_openai(self, image_path: str) -> List[DamageDetection]:
        """Analyze image using OpenAI GPT-4V"""
        try:
            import openai
            
            if self.openai_api_key == "your_openai_api_key_here":
                print("⚠️ OpenAI API key not configured")
                return []
            
            openai.api_key = self.openai_api_key
            base64_image = self.encode_image_base64(image_path)
            
            response = openai.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.create_balanced_prompt()},
                            {
                                "type": "image_url",
                                "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )
            
            content = response.choices[0].message.content.strip()
            print(f"🤖 OpenAI GPT-4V response: {content[:100]}...")
            
            # Parse JSON response
            if content.startswith('[') and content.endswith(']'):
                detections_data = json.loads(content)
                detections = []
                for item in detections_data:
                    detection = DamageDetection(
                        part_name=item['part_name'],
                        severity=item['severity'],
                        confidence=float(item['confidence']),
                        description=item['description'],
                        repair_needed=item['repair_needed'],
                        estimated_area_percentage=float(item['estimated_area_percentage'])
                    )
                    detections.append(detection)
                return detections
            else:
                print("⚠️ OpenAI returned non-JSON response")
                return []
                
        except Exception as e:
            print(f"❌ OpenAI analysis failed: {e}")
            return []

    def analyze_with_claude(self, image_path: str) -> List[DamageDetection]:
        """Analyze image using Claude 3.5 Sonnet"""
        try:
            if self.claude_api_key == "your_claude_api_key_here":
                print("⚠️ Claude API key not configured")
                return []
            
            base64_image = self.encode_image_base64(image_path)
            
            headers = {
                "Content-Type": "application/json",
                "x-api-key": self.claude_api_key,
                "anthropic-version": "2023-06-01"
            }
            
            data = {
                "model": "claude-3-5-sonnet-20241022",
                "max_tokens": 1000,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.create_balanced_prompt()},
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/jpeg",
                                    "data": base64_image
                                }
                            }
                        ]
                    }
                ]
            }
            
            response = requests.post(
                "https://api.anthropic.com/v1/messages",
                headers=headers,
                json=data
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['content'][0]['text'].strip()
                print(f"🤖 Claude 3.5 response: {content[:100]}...")
                
                # Parse JSON response
                if content.startswith('[') and content.endswith(']'):
                    detections_data = json.loads(content)
                    detections = []
                    for item in detections_data:
                        detection = DamageDetection(
                            part_name=item['part_name'],
                            severity=item['severity'],
                            confidence=float(item['confidence']),
                            description=item['description'],
                            repair_needed=item['repair_needed'],
                            estimated_area_percentage=float(item['estimated_area_percentage'])
                        )
                        detections.append(detection)
                    return detections
                else:
                    print("⚠️ Claude returned non-JSON response")
                    return []
            else:
                print(f"❌ Claude API error: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ Claude analysis failed: {e}")
            return []

    def analyze_with_gemini(self, image_path: str) -> List[DamageDetection]:
        """Analyze image using Google Gemini Pro Vision"""
        try:
            if self.gemini_api_key == "your_gemini_api_key_here":
                print("⚠️ Gemini API key not configured")
                return []

            base64_image = self.encode_image_base64(image_path)

            # Gemini API endpoint - using correct model name
            url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={self.gemini_api_key}"

            headers = {
                "Content-Type": "application/json"
            }

            # Create Gemini-specific prompt
            prompt = self.create_gemini_prompt()

            data = {
                "contents": [
                    {
                        "parts": [
                            {"text": prompt},
                            {
                                "inline_data": {
                                    "mime_type": "image/jpeg",
                                    "data": base64_image
                                }
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 1000
                }
            }

            response = requests.post(url, headers=headers, json=data)

            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    content = result['candidates'][0]['content']['parts'][0]['text'].strip()
                    print(f"🤖 Gemini Pro Vision response: {content[:100]}...")

                    # Parse JSON response
                    if content.startswith('[') and content.endswith(']'):
                        detections_data = json.loads(content)
                        detections = []
                        for item in detections_data:
                            detection = DamageDetection(
                                part_name=item['part_name'],
                                severity=item['severity'],
                                confidence=float(item['confidence']),
                                description=item['description'],
                                repair_needed=item['repair_needed'],
                                estimated_area_percentage=float(item['estimated_area_percentage'])
                            )
                            detections.append(detection)
                        return detections
                    else:
                        print("⚠️ Gemini returned non-JSON response")
                        return []
                else:
                    print("⚠️ Gemini returned empty response")
                    return []
            else:
                print(f"❌ Gemini API error: {response.status_code} - {response.text}")
                return []

        except Exception as e:
            print(f"❌ Gemini analysis failed: {e}")
            return []

    def create_gemini_prompt(self) -> str:
        """Create Gemini-specific prompt for damage detection"""
        return f"""
You are an expert vehicle damage assessor. Analyze this vehicle image with extreme precision to avoid false positives.

CRITICAL ANTI-FALSE-POSITIVE INSTRUCTIONS:
- ONLY report damage if you can see OBVIOUS, UNMISTAKABLE damage
- DO NOT report damage based on shadows, reflections, or normal vehicle features
- Clean vehicles with normal styling features are NOT damaged
- Be EXTREMELY conservative - false positives are unacceptable

SPECIAL FOCUS ON BUMPERS:
- Bumpers have natural edges, curves, and design lines - these are NOT damage
- DO NOT report bumper damage unless you see OBVIOUS cracks, holes, or major deformation
- Normal bumper styling, trim, or panel gaps are NOT damage
- If uncertain about bumper damage, DO NOT report it

Vehicle parts to analyze: {', '.join(self.vehicle_parts)}

ONLY REPORT if you can see UNMISTAKABLE damage:
- Large, obvious dents with clear deformation
- Completely broken/shattered parts
- Missing components or pieces
- Deep scratches exposing metal/primer
- Major cracks or holes
- Clearly detached or hanging parts

DO NOT REPORT:
- Normal vehicle styling or design features
- Shadows, reflections, or lighting effects
- Minor surface marks or dirt
- Panel gaps or seams
- Uncertain or unclear areas
- Anything that could be normal vehicle condition

For each CLEARLY damaged part, provide:
1. Part name (exactly from the list above)
2. Severity (minor/moderate/severe)
3. Confidence (0.8-1.0, only report if 80%+ certain)
4. Specific description of OBVIOUS damage
5. Repair needed (true/false)
6. Estimated damage area percentage (10-100%)

Respond ONLY with JSON array:
[
  {{
    "part_name": "Door",
    "severity": "severe",
    "confidence": 0.9,
    "description": "Large obvious dent with clear metal deformation on driver door",
    "repair_needed": true,
    "estimated_area_percentage": 30.0
  }}
]

If no OBVIOUS damage is visible, respond with: []

REMEMBER: False positives are unacceptable. When in doubt, return empty array.
"""

    def analyze_with_hybrid(self, image_path: str) -> List[DamageDetection]:
        """Use multiple LLMs and combine results for better accuracy"""
        try:
            print("🤖 Running hybrid analysis with multiple LLMs...")
            
            all_detections = []
            
            # Try OpenAI
            if self.openai_api_key != "your_openai_api_key_here":
                openai_detections = self.analyze_with_openai(image_path)
                all_detections.extend([(d, "openai") for d in openai_detections])
            
            # Try Claude
            if self.claude_api_key != "your_claude_api_key_here":
                claude_detections = self.analyze_with_claude(image_path)
                all_detections.extend([(d, "claude") for d in claude_detections])
            
            # Combine and validate results
            return self.combine_hybrid_results(all_detections)
                
        except Exception as e:
            print(f"❌ Hybrid analysis failed: {e}")
            return []

    def combine_hybrid_results(self, all_detections: List[tuple]) -> List[DamageDetection]:
        """Combine results from multiple LLMs for better accuracy"""
        if not all_detections:
            return []
        
        # Group detections by part
        part_detections = {}
        for detection, source in all_detections:
            part = detection.part_name
            if part not in part_detections:
                part_detections[part] = []
            part_detections[part].append((detection, source))
        
        # Validate and combine
        final_detections = []
        for part, detections in part_detections.items():
            if len(detections) >= 2:  # Multiple LLMs agree
                # Use the detection with highest confidence
                best_detection = max(detections, key=lambda x: x[0].confidence)[0]
                final_detections.append(best_detection)
                print(f"✅ Hybrid consensus: {part} damage confirmed by multiple LLMs")
            elif len(detections) == 1 and detections[0][0].confidence >= 0.8:
                # Single LLM with high confidence
                final_detections.append(detections[0][0])
                print(f"✅ High confidence single detection: {part}")
        
        return final_detections

    def analyze_image(self, image_path: str) -> List[DamageDetection]:
        """Main analysis method that routes to selected LLM"""
        print(f"🔍 Starting {self.selected_llm.upper()} damage analysis on: {image_path}")
        
        if self.selected_llm == "openai":
            return self.analyze_with_openai(image_path)
        elif self.selected_llm == "claude":
            return self.analyze_with_claude(image_path)
        elif self.selected_llm == "gemini":
            return self.analyze_with_gemini(image_path)
        elif self.selected_llm == "hybrid":
            return self.analyze_with_hybrid(image_path)
        else:
            print(f"❌ Unknown LLM: {self.selected_llm}")
            return []

if __name__ == "__main__":
    # Test the multi-LLM detector
    detector = MultiLLMDamageDetector()
    
    print("🔍 MULTI-LLM DAMAGE DETECTOR TEST")
    print("=" * 50)
    print(f"Selected LLM: {detector.selected_llm.upper()}")
    print(f"OpenAI configured: {'✅' if detector.openai_api_key != 'your_openai_api_key_here' else '❌'}")
    print(f"Claude configured: {'✅' if detector.claude_api_key != 'your_claude_api_key_here' else '❌'}")
    print(f"Gemini configured: {'✅' if detector.gemini_api_key != 'your_gemini_api_key_here' else '❌'}")
