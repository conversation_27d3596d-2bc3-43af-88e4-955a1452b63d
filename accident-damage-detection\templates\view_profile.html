<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Profile - Car Damage Detection</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.5/gsap.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(45deg, #6ab7f5, #f086d3);
            padding: 20px;
        }
        .nav-buttons {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
        }
        .home-button, .user-profile {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .home-button:hover, .user-profile:hover {
            background-color: rgba(255, 255, 255, 1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }
        .home-button i, .user-profile i {
            font-size: 1.2rem;
            color: #3498db;
        }
        .user-profile {
            position: relative;
        }
        .profile-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 10px;
            display: none;
            z-index: 1000;
        }
        .user-profile:hover .profile-dropdown {
            display: block;
        }
        .profile-dropdown a {
            display: block;
            padding: 8px 15px;
            color: #2c3e50;
            text-decoration: none;
            transition: background-color 0.3s ease;
            white-space: nowrap;
        }
        .profile-dropdown a:hover {
            background-color: #f1f1f1;
        }
        .profile-container {
            background-color: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            width: 800px;
            max-width: 90%;
        }
        h2 {
            text-align: center;
            color: #333;
            margin-top: 0;
            margin-bottom: 1.5rem;
        }
        .profile-row {
            display: flex;
            gap: 2rem;
        }
        .profile-column {
            flex: 1;
        }
        .profile-item {
            margin-bottom: 1.25rem;
        }
        .profile-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 0.25rem;
        }
        .profile-value {
            color: #333;
        }
        .edit-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 1rem;
            margin-left: auto;
            margin-right: auto;
        }
        .edit-button:hover {
            background-color: #45a049;
        } 
    </style>
</head>
<body>
    <div class="nav-buttons">
        <a href="/" class="home-button">
            <i class="fas fa-home"></i>
        </a>
        <div class="user-profile">
            <i class="fas fa-user-circle"></i>
            <div class="profile-dropdown">
                <a href="/dashboard">Dashboard</a>
                <a href="/logout">Logout</a>
            </div>
        </div>
    </div>

    <div class="profile-container">
        <h2>User Profile</h2>
        <div class="profile-row">
            <div class="profile-column">
                <div class="profile-item">
                    <div class="profile-label">Full Name</div>
                    <div class="profile-value" id="fullName">{{ user_info['name'] }}</div>
                </div>
                <div class="profile-item">
                    <div class="profile-label">Email</div>
                    <div class="profile-value" id="email">{{ user_info['email'] }}</div>
                </div>
                <div class="profile-item">
                    <div class="profile-label">Phone Number</div>
                    <div class="profile-value" id="phoneNumber">{{ user_info['contact_number'] }}</div>
                </div>
                <div class="profile-item">
                    <div class="profile-label">Full Address</div>
                    <div class="profile-value" id="fullAddress">{{ user_info['address'] }}</div>
                </div>
            </div>
            <div class="profile-column">
                <div class="profile-item">
                    <div class="profile-label">Vehicle ID</div>
                    <div class="profile-value" id="vehicleId">{{ user_info['vehicle_id'] }}</div>
                </div>
                <div class="profile-item">
                    <div class="profile-label">Car Brand</div>
                    <div class="profile-value" id="carBrand">{{ user_info['car_brand'] }}</div>
                </div>
                <div class="profile-item">
                    <div class="profile-label">Car Model</div>
                    <div class="profile-value" id="carModel">{{ user_info['model'] }}</div>
                </div>
            </div>
        </div>
        <a href="{{ url_for('edit_profile') }}" class="edit-button">Edit Profile</a>
    </div>
</body>
</html>
