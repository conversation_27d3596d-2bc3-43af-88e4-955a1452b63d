{% extends "base.html" %}

{% block title %}Sign Up - VehicleCare Pro{% endblock %}

{% block extra_css %}
<style>
    .signup-container {
        max-width: 600px;
        margin: 2rem auto;
    }

    .signup-card {
        background: var(--bg-primary);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        border: 1px solid var(--border-color);
    }

    .signup-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .signup-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .signup-subtitle {
        opacity: 0.9;
        font-size: 1rem;
    }

    .signup-form {
        padding: 2rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .form-group-full {
        grid-column: 1 / -1;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.875rem;
    }

    .form-input, .form-select {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background: var(--bg-primary);
        color: var(--text-primary);
        box-sizing: border-box;
    }

    .form-input:focus, .form-select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        transform: translateY(-1px);
    }

    .form-input:hover, .form-select:hover {
        border-color: var(--primary-color);
    }

    .submit-btn {
        width: 100%;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .submit-btn:active {
        transform: translateY(0);
    }

    .login-link {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-color);
        color: var(--text-secondary);
    }

    .login-link a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.2s ease;
    }

    .login-link a:hover {
        color: var(--primary-dark);
        text-decoration: underline;
    }

    .input-icon {
        position: relative;
    }

    .input-icon i {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        font-size: 0.875rem;
    }

    .input-icon .form-input {
        padding-left: 2.5rem;
    }

    .vehicle-info {
        background: var(--bg-tertiary);
        padding: 1.5rem;
        border-radius: var(--border-radius);
        margin-bottom: 1.5rem;
    }

    .vehicle-info h3 {
        margin: 0 0 1rem 0;
        color: var(--text-primary);
        font-size: 1.1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .signup-container {
            margin: 1rem;
        }
        
        .signup-form {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="signup-container">
    <div class="signup-card">
        <div class="signup-header">
            <h1 class="signup-title">Create Your Account</h1>
            <p class="signup-subtitle">Join VehicleCare Pro and start analyzing vehicle damage with AI</p>
        </div>
        
        <div class="signup-form">
            <form id="signup-form" action="{{ url_for('signup') }}" method="POST">
                <!-- Personal Information -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="name" class="form-label">
                            <i class="fas fa-user"></i> Full Name
                        </label>
                        <input type="text" id="name" name="name" class="form-input" placeholder="Enter your full name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i> Email Address
                        </label>
                        <input type="email" id="email" name="email" class="form-input" placeholder="Enter your email" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i> Password
                        </label>
                        <input type="password" id="password" name="password" class="form-input" placeholder="Create a strong password" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phoneNumber" class="form-label">
                            <i class="fas fa-phone"></i> Phone Number
                        </label>
                        <input type="tel" id="phoneNumber" name="phoneNumber" class="form-input" placeholder="Enter your phone number" required>
                    </div>
                </div>
                
                <div class="form-group form-group-full">
                    <label for="address" class="form-label">
                        <i class="fas fa-map-marker-alt"></i> Address
                    </label>
                    <input type="text" id="address" name="address" class="form-input" placeholder="Enter your full address" required>
                </div>
                
                <!-- Vehicle Information -->
                <div class="vehicle-info">
                    <h3><i class="fas fa-car"></i> Vehicle Information</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="vehicleId" class="form-label">Vehicle ID/License Plate</label>
                            <input type="text" id="vehicleId" name="vehicleId" class="form-input" placeholder="e.g., ABC123" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="carBrand" class="form-label">Car Brand</label>
                            <select id="carBrand" name="carBrand" class="form-select" required>
                                <option value="">Select Brand</option>
                                <option value="HONDA">Honda</option>
                                <option value="TOYOTA">Toyota</option>
                                <option value="MARUTI SUZUKI">Maruti Suzuki</option>
                                <option value="HYUNDAI">Hyundai</option>
                                <option value="NISSAN">Nissan</option>
                                <option value="SKODA">Skoda</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="carModel" class="form-label">Car Model</label>
                        <input type="text" id="carModel" name="carModel" class="form-input" placeholder="e.g., City, Swift, Camry" required>
                    </div>
                </div>
                
                <button type="submit" class="submit-btn">
                    <i class="fas fa-user-plus"></i>
                    Create Account
                </button>
            </form>
            
            <div class="login-link">
                Already have an account? <a href="{{ url_for('login') }}">Sign in here</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
