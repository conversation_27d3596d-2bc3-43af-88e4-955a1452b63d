<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Profile - Car Damage Detection</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.5/gsap.min.js"></script>
    <style>
        
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(45deg, #6ab7f5, #f086d3);
            padding: 20px;
        }
        .edit-profile-container {
            background-color: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            width: 800px;
            max-width: 90%;
        }
        h2 {
            text-align: center;
            color: #333;
            margin-top: 0;
            margin-bottom: 1.5rem;
        }
        .form-row {
            display: flex;
            gap: 2rem;
        }
        .form-column {
            flex: 1;
        }
        .input-group {
            margin-bottom: 1.25rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
        }
        input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            box-sizing: border-box;
        }
        .submit-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            display: block;
            margin: 1rem auto 0;
            width: 100%;
        }
        .submit-button:hover {
            background-color: #45a049;
        }
        .error-message {
            color: red;
            text-align: center;
            margin-top: 1rem;
        }
        .nav-buttons {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
        }

        .home-button, .user-profile {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .home-button:hover, .user-profile:hover {
            background-color: rgba(255, 255, 255, 1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        .home-button i, .user-profile i {
            font-size: 1.2rem;
            color: #3498db;
        }

        .user-profile {
            position: relative;
        }

        .profile-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 10px;
            display: none;
            z-index: 1000;
        }

        .user-profile:hover .profile-dropdown {
            display: block;
        }

        .profile-dropdown a {
            display: block;
            padding: 8px 15px;
            color: #2c3e50;
            text-decoration: none;
            transition: background-color 0.3s ease;
            white-space: nowrap;
        }

        .profile-dropdown a:hover {
            background-color: #f1f1f1;
        }
    </style>
</head>
<body>

    <div class="nav-buttons">
        <a href="/" class="home-button">
            <i class="fas fa-home"></i>
        </a>
        <div class="user-profile">
            <i class="fas fa-user-circle"></i>
            <div class="profile-dropdown">
                <a href="/dashboard">Dashboard</a>
                <a href="/logout">Logout</a>
            </div>
        </div>
    </div>
    <div class="edit-profile-container">
        <h2>Edit Profile</h2>
        <form id="edit-profile-form" action="{{ url_for('edit_profile') }}" method="POST">
            <div class="form-row">
                <div class="form-column">
                    <div class="input-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" name="name" value="{{ user_info.name }}" required>
                    </div>
                    <div class="input-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" value="{{ user_info.email }}" required pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$">
                    </div>
                    <div class="input-group">
                        <label for="phoneNumber">Phone Number</label>
                        <input type="tel" id="phoneNumber" name="phoneNumber" value="{{ user_info.contact_number }}" required>
                    </div>
                    <div class="input-group">
                        <label for="address">Full Address</label>
                        <input type="text" id="address" name="address" value="{{ user_info.address }}" required>
                    </div>
                </div>
                <div class="form-column">
                    <div class="input-group">
                        <label for="vehicleId">Vehicle ID</label>
                        <input type="text" id="vehicleId" name="vehicleId" value="{{ user_info.vehicle_id }}" required>
                    </div>
                    <div class="input-group">
                        <label for="carBrand">Car Brand</label>
                        <input type="text" id="carBrand" name="carBrand" value="{{ user_info.car_brand }}" required>
                    </div>
                    <div class="input-group">
                        <label for="carModel">Car Model</label>
                        <input type="text" id="carModel" name="carModel" value="{{ user_info.model }}" required>
                    </div>
                </div>
            </div>
            <button type="submit" class="submit-button">Update Profile</button>
        </form>
        <div class="error-message" id="error-message"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            gsap.from('.edit-profile-container', {duration: 1, opacity: 0, y: -50, ease: 'power3.out'});
            gsap.from('input, button', {duration: 0.5, opacity: 0, y: 20, stagger: 0.1, ease: 'power2.out', delay: 0.5});
        });
    </script>
</body>
</html>