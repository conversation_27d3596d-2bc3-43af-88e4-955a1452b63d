#!/usr/bin/env python3
"""
LLM Configuration Tool for Vehicle Damage Detection
"""

import os
import json

def display_llm_options():
    """Display available LLM options with their pros and cons"""
    
    print("🤖 AVAILABLE LLMs FOR DAMAGE DETECTION")
    print("=" * 60)
    
    options = {
        "1": {
            "name": "OpenAI GPT-4V",
            "pros": ["Good general vision", "Easy integration", "You already have API key"],
            "cons": ["False positives on clean vehicles", "Inconsistent results"],
            "accuracy": "Medium",
            "cost": "High",
            "setup": "Already configured"
        },
        "2": {
            "name": "Claude 3.5 Sonnet",
            "pros": ["Excellent accuracy", "Low false positives", "Better reasoning"],
            "cons": ["Need new API key", "Slightly more expensive"],
            "accuracy": "High",
            "cost": "High",
            "setup": "Need API key from Anthropic"
        },
        "3": {
            "name": "Google Gemini Pro Vision",
            "pros": ["Good automotive analysis", "Competitive pricing"],
            "cons": ["Need Google AI Studio setup", "Less tested"],
            "accuracy": "Medium-High",
            "cost": "Medium",
            "setup": "Need Google AI Studio API key"
        },
        "4": {
            "name": "Hybrid (Multiple LLMs)",
            "pros": ["Highest accuracy", "Cross-validation", "Best reliability"],
            "cons": ["Higher cost", "Need multiple API keys"],
            "accuracy": "Very High",
            "cost": "Very High",
            "setup": "Need 2+ API keys"
        },
        "5": {
            "name": "Computer Vision Only",
            "pros": ["Fast", "No API costs", "Consistent"],
            "cons": ["Lower accuracy", "Needs training data"],
            "accuracy": "Medium",
            "cost": "Free",
            "setup": "Already available"
        }
    }
    
    for key, option in options.items():
        print(f"\n{key}. {option['name']}")
        print(f"   Accuracy: {option['accuracy']}")
        print(f"   Cost: {option['cost']}")
        print(f"   Setup: {option['setup']}")
        print(f"   Pros: {', '.join(option['pros'])}")
        print(f"   Cons: {', '.join(option['cons'])}")
    
    return options

def get_api_key_instructions():
    """Provide instructions for getting API keys"""
    
    instructions = {
        "claude": {
            "url": "https://console.anthropic.com/",
            "steps": [
                "1. Go to https://console.anthropic.com/",
                "2. Sign up for an account",
                "3. Go to API Keys section",
                "4. Create a new API key",
                "5. Copy the key (starts with 'sk-ant-')"
            ],
            "cost": "$15 per 1M tokens (similar to OpenAI)"
        },
        "gemini": {
            "url": "https://aistudio.google.com/",
            "steps": [
                "1. Go to https://aistudio.google.com/",
                "2. Sign in with Google account",
                "3. Create a new API key",
                "4. Copy the key",
                "5. Enable Gemini Pro Vision model"
            ],
            "cost": "$7 per 1M tokens (cheaper than OpenAI)"
        }
    }
    
    return instructions

def configure_llm_selection():
    """Interactive LLM configuration"""
    
    print("🔧 LLM CONFIGURATION FOR DAMAGE DETECTION")
    print("=" * 60)
    
    # Display current configuration
    current_llm = os.getenv('DAMAGE_DETECTION_LLM', 'openai')
    print(f"Current LLM: {current_llm.upper()}")
    
    # Show options
    options = display_llm_options()
    
    print("\n" + "=" * 60)
    print("RECOMMENDATIONS BASED ON YOUR ISSUE:")
    print("=" * 60)
    
    print("🎯 FOR YOUR FALSE POSITIVE ISSUE:")
    print("   BEST: Claude 3.5 Sonnet (option 2)")
    print("   REASON: Much better at distinguishing real vs false damage")
    print("   SETUP: Need Anthropic API key")
    
    print("\n🎯 FOR MAXIMUM ACCURACY:")
    print("   BEST: Hybrid approach (option 4)")
    print("   REASON: Multiple LLMs cross-validate results")
    print("   SETUP: Need Claude + your existing OpenAI key")
    
    print("\n🎯 FOR COST-EFFECTIVE:")
    print("   BEST: Google Gemini (option 3)")
    print("   REASON: Good accuracy, lower cost")
    print("   SETUP: Need Google AI Studio API key")
    
    # Get user choice
    while True:
        choice = input("\nSelect option (1-5) or 'q' to quit: ").strip()
        
        if choice.lower() == 'q':
            return
        
        if choice in options:
            selected = options[choice]
            print(f"\n✅ You selected: {selected['name']}")
            
            # Handle configuration based on choice
            if choice == "1":
                configure_openai()
            elif choice == "2":
                configure_claude()
            elif choice == "3":
                configure_gemini()
            elif choice == "4":
                configure_hybrid()
            elif choice == "5":
                configure_computer_vision()
            
            break
        else:
            print("❌ Invalid choice. Please select 1-5 or 'q'.")

def configure_claude():
    """Configure Claude 3.5 Sonnet"""
    
    print("\n🤖 CONFIGURING CLAUDE 3.5 SONNET")
    print("=" * 50)
    
    instructions = get_api_key_instructions()["claude"]
    
    print("📋 TO GET CLAUDE API KEY:")
    for step in instructions["steps"]:
        print(f"   {step}")
    
    print(f"\n💰 Cost: {instructions['cost']}")
    
    api_key = input("\nEnter your Claude API key (or press Enter to skip): ").strip()
    
    if api_key:
        # Update .env file
        update_env_file("CLAUDE_API_KEY", api_key)
        update_env_file("DAMAGE_DETECTION_LLM", "claude")
        
        print("✅ Claude configured successfully!")
        print("🎯 Your app will now use Claude 3.5 Sonnet for damage detection")
        print("📈 Expected improvements:")
        print("   • Much fewer false positives")
        print("   • Better accuracy on real damage")
        print("   • More consistent results")
    else:
        print("⚠️ Skipped Claude configuration")

def configure_hybrid():
    """Configure hybrid approach"""
    
    print("\n🤖 CONFIGURING HYBRID APPROACH")
    print("=" * 50)
    
    print("🎯 Hybrid uses multiple LLMs for maximum accuracy:")
    print("   • Your existing OpenAI GPT-4V")
    print("   • Claude 3.5 Sonnet (recommended)")
    print("   • Results are cross-validated")
    
    # Check current keys
    has_openai = os.getenv('OPENAI_API_KEY', 'your_openai_api_key_here') != 'your_openai_api_key_here'
    has_claude = os.getenv('CLAUDE_API_KEY', 'your_claude_api_key_here') != 'your_claude_api_key_here'
    
    print(f"\n📊 Current API Keys:")
    print(f"   OpenAI: {'✅ Configured' if has_openai else '❌ Missing'}")
    print(f"   Claude: {'✅ Configured' if has_claude else '❌ Missing'}")
    
    if not has_claude:
        print("\n🔧 You need Claude API key for hybrid approach:")
        configure_claude()
    
    if has_openai or has_claude:
        update_env_file("DAMAGE_DETECTION_LLM", "hybrid")
        print("✅ Hybrid approach configured!")
        print("🎯 Your app will now use multiple LLMs for maximum accuracy")

def update_env_file(key: str, value: str):
    """Update .env file with new key-value pair"""
    
    env_path = ".env"
    lines = []
    
    # Read existing .env file
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            lines = f.readlines()
    
    # Update or add the key
    key_found = False
    for i, line in enumerate(lines):
        if line.startswith(f"{key}="):
            lines[i] = f"{key}={value}\n"
            key_found = True
            break
    
    if not key_found:
        lines.append(f"{key}={value}\n")
    
    # Write back to .env file
    with open(env_path, 'w') as f:
        f.writelines(lines)
    
    print(f"✅ Updated .env: {key}={value}")

def configure_openai():
    """Keep current OpenAI configuration"""
    print("\n✅ Keeping current OpenAI GPT-4V configuration")
    print("⚠️ Note: You reported false positive issues with OpenAI")
    print("💡 Consider trying Claude or Hybrid for better accuracy")

def configure_gemini():
    """Configure Google Gemini"""
    print("\n🤖 CONFIGURING GOOGLE GEMINI PRO VISION")
    print("=" * 50)
    print("📋 TO GET GEMINI API KEY:")
    print("   1. Go to https://aistudio.google.com/")
    print("   2. Sign in with Google account")
    print("   3. Create API key")
    print("   4. Enable Gemini Pro Vision")
    print("\n⚠️ Note: Gemini integration is experimental")

def configure_computer_vision():
    """Configure computer vision only"""
    print("\n🤖 CONFIGURING COMPUTER VISION ONLY")
    print("=" * 50)
    update_env_file("DAMAGE_DETECTION_LLM", "cv_only")
    print("✅ Computer vision only mode configured")
    print("⚠️ Note: Lower accuracy than LLM approaches")

if __name__ == "__main__":
    configure_llm_selection()
