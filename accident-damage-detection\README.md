# 🤖 LLM-Powered Vehicle Damage Detection and Repair Cost Estimation

[LLM-Powered Vehicle Damage Detection and Repair Cost Estimation](https://github.com/user-attachments/assets/5b6b8a1d-cb9c-4b57-87a5-dc80ceeb955e)

An advanced AI-powered **Vehicle Damage Detection System** that uses **Large Language Models (LLM)** with vision capabilities to intelligently detect and assess vehicle damage, then estimate repair costs. The system leverages **GPT-4V** for sophisticated damage analysis and a web-based interface built with **Flask** for seamless user interaction. This project helps automate the process of assessing vehicle damage with human-like intelligence, making it highly efficient for insurance companies and repair centers.

---

## Table of Contents

- 🚗 [Project Overview](#project-overview)
- ⚙️ [Features](#features)
- 🛠️ [Technologies Used](#technologies-used)
- 📝 [Setup and Installation](#setup-and-installation)
- 💻 [Usage](#usage)
- 🤖 [Model Information](#model-information)
- 🚀 [Future Improvements](#future-improvements)
---

## 🚗 Project Overview

The **Vehicle Damage Detection System** is designed to help users upload car images and detect damaged parts, including:
- **Bonnet**
- **Bumper**
- **<PERSON><PERSON>**
- **Door**
- **Fender**
- **Light**
- **Windshield**

The detected parts are highlighted in the image, and the system estimates repair costs based on the detected damage.

### Key Objectives:
1. **Damage Detection**: Accurately detect damaged car parts using object detection models.
2. **Cost Estimation**: Estimate repair costs for the damaged parts detected in the vehicle image.
3. **Database Integration**: Store user details, car information, spare parts, and repair costs in a MySQL database.
4. **User-Friendly Interface**: Provide an intuitive and simple web-based interface for users to upload images and view results.

---

## ⚙️ Features

- **Object Detection**: Detect damaged car parts using YOLOv8.
- **Cost Estimation**: Estimate repair costs based on detected damage.
- **Image Upload**: Upload car images via a web-based interface.
- **Real-Time Results**: Display detection results and estimated costs in real-time.
- **Database Management**: Store vehicle, user, and spare parts information using MySQL.
- **Visual Damage Display**: View damaged parts highlighted in the uploaded image.

---

## 🛠️ Technologies Used

- **Backend**: 
  - Flask (Python web framework)
  - YOLOv8 (for object detection)
  - MySQL (for database management)
- **Frontend**: 
  - HTML/CSS (for user interface)
  - JavaScript (for frontend logic)
- **Libraries**: 
  - OpenCV (for image processing)
  - Matplotlib (for image visualization)
  - Ultralytics (YOLOv8 integration)

---
## 💻 Usage
- **Sign Up**: Create an account by filling out your personal details and vehicle information.
- **Log In**: Use your credentials to log in to the system.
- **Upload Image**: Upload an image of the damaged vehicle.
- **AI Analysis**: The LLM analyzes the image and identifies damage with detailed descriptions.
- **View Results**: See detected damage with severity levels, confidence scores, and repair cost estimates.
- **Download Image**: Download the annotated image showing detected damage areas.

## 🧠 LLM Features
- **Intelligent Analysis**: Uses GPT-4V to understand vehicle damage context
- **Damage Severity**: Categorizes damage as minor, moderate, or severe
- **Detailed Descriptions**: Provides specific descriptions of each damage type
- **Confidence Scoring**: Shows reliability of each detection (0-100%)
- **Quality Validation**: Automatically validates response quality and adjusts thresholds
- **No Training Required**: Works out-of-the-box without custom model training

## 🤖 AI Model Information
- **GPT-4V (Vision Language Model)**: Advanced AI that understands both images and text, providing intelligent damage analysis
- **Damage Assessment**: Identifies damaged vehicle parts including bonnet, bumper, dickey, door, fender, light, and windshield
- **Severity Analysis**: Categorizes damage as minor, moderate, or severe with detailed descriptions
- **Confidence Scoring**: Provides reliability scores for each detection to ensure accurate assessments
- **No Training Required**: Uses pre-trained intelligence, eliminating the need for custom model training

## 🚀 Future Improvements
- **Multi-language Support**: Extend the system to support multiple languages.
- **Damage Severity Analysis**: Improve the system to estimate the extent of damage in more detail.
- **Insurance Integration**: Allow integration with insurance companies for seamless claim processing.
- **Mobile App**: Develop a mobile app version for on-the-go damage detection.

---

## 📝 Setup and Installation

### Prerequisites
- Python 3.11.8
- MySQL Server
- Virtual Environment (optional, but recommended)
  
### Installation Steps

1. **Navigate to the Project Directory**:
   ```bash
   cd accident-damage-detection
   ```

2. **Install Required Packages**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set Up Environment Variables**:
   - Copy the example environment file:
   ```bash
   cp .env.example .env
   ```
   - Edit `.env` and add your configuration:
   ```bash
   SECRET_KEY=your_secret_key_here_change_this_in_production
   OPENAI_API_KEY=your_openai_api_key_here
   ```
   - Get your OpenAI API key from [OpenAI Platform](https://platform.openai.com/)

4. **Configure Database**:
   - Update the database credentials in `config.py` if needed:
   ```python
   mysql_credentials = {
       'host': '127.0.0.1',
       'user': 'root',
       'password': 'Root@123',
       'database': 'vehicle_damage_detection'
   }
   ```

5. **Set Up MySQL Database** (Optional - if database doesn't exist):
   ```bash
   python setup_database.py
   ```

6. **Test the LLM Setup** (Optional but recommended):
   ```bash
   python test_llm_damage_detection.py
   ```

7. **Run the Application**:
   ```bash
   python app.py
   ```

8. **Access the Application**:
   - Open your web browser and go to: `http://127.0.0.1:8080`

### Quick Start (Current Setup)
The application is already configured and running! Simply open your browser and navigate to:
**http://127.0.0.1:5000**
