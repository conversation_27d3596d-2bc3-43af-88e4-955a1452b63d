#!/usr/bin/env python3
"""
Debug dashboard pricing issues step by step
"""

import sqlite3
import os
from app import get_part_prices, normalize_brand_name, normalize_model_name
from llm_damage_detector import LLMDamageDetector

def check_current_users():
    """Check all users and their vehicle compatibility"""
    
    print("🔍 CHECKING ALL USERS AND VEHICLE COMPATIBILITY")
    print("=" * 60)
    
    conn = sqlite3.connect('vehicle_damage.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    cursor.execute("SELECT email, car_brand, model FROM user_info")
    users = cursor.fetchall()
    
    compatible_users = []
    
    for user in users:
        email = user['email']
        car_brand = user['car_brand']
        model = user['model']
        
        print(f"\n👤 {email}")
        print(f"🚗 Vehicle: {car_brand} {model}")
        
        # Check pricing compatibility
        normalized_brand = normalize_brand_name(car_brand)
        normalized_model = normalize_model_name(model)
        
        cursor.execute("""
            SELECT COUNT(*) as count FROM car_models 
            WHERE UPPER(brand) = UPPER(?) AND UPPER(model) = UPPER(?)
        """, (normalized_brand, normalized_model))
        
        count = cursor.fetchone()['count']
        
        if count > 0:
            print(f"✅ COMPATIBLE - {count} pricing entries found")
            compatible_users.append(email)
        else:
            print(f"❌ INCOMPATIBLE - No pricing data")
    
    conn.close()
    
    print(f"\n🎯 COMPATIBLE USERS: {len(compatible_users)}")
    for email in compatible_users:
        print(f"   ✅ {email}")
    
    return compatible_users

def test_llm_detection_flow():
    """Test the complete LLM detection flow"""
    
    print("\n🤖 TESTING LLM DETECTION FLOW")
    print("=" * 60)
    
    try:
        # Initialize detector
        detector = LLMDamageDetector()
        print(f"✅ LLM Detector initialized")
        print(f"   API Key: {detector.api_key[:15]}...")
        print(f"   Model: {detector.model}")
        
        # Test demo detections (what happens when no real image)
        print(f"\n📋 Testing demo detections...")
        demo_detections = detector._get_demo_detections()
        print(f"   Generated: {len(demo_detections)} detections")
        
        for detection in demo_detections:
            print(f"   • {detection.part_name}: {detection.severity} ({detection.confidence:.1%})")
        
        # Test filtering
        filtered = detector.filter_by_confidence(demo_detections)
        print(f"\n🔍 After confidence filtering: {len(filtered)} detections")
        
        # Test class counts
        class_counts = detector.get_part_counts_for_pricing(filtered)
        print(f"🔢 Class counts for pricing: {class_counts}")
        
        if not class_counts:
            print("❌ NO CLASS COUNTS - This causes pricing error!")
            return False
        else:
            print("✅ Class counts generated successfully")
            return True
            
    except Exception as e:
        print(f"❌ LLM Detection failed: {e}")
        return False

def test_pricing_with_compatible_user():
    """Test pricing with a known compatible user"""
    
    print("\n💰 TESTING PRICING WITH COMPATIBLE USER")
    print("=" * 60)
    
    # Use demo user (guaranteed compatible)
    email = "<EMAIL>"
    
    # Test with sample damage
    sample_class_counts = {3: 1, 1: 1, 5: 1}  # Door, Bumper, Light
    
    print(f"🧪 Testing pricing for: {email}")
    print(f"🔢 Sample damage: {sample_class_counts}")
    
    try:
        part_prices = get_part_prices(email, sample_class_counts)
        
        if part_prices:
            total = sum(item['total'] for item in part_prices.values())
            print(f"✅ Pricing successful!")
            print(f"   Total cost: ₹{total:,}")
            for part, details in part_prices.items():
                print(f"   {part}: {details['count']} × ₹{details['price']:,} = ₹{details['total']:,}")
            return True
        else:
            print(f"❌ No pricing returned")
            return False
            
    except Exception as e:
        print(f"❌ Pricing test failed: {e}")
        return False

def simulate_dashboard_flow():
    """Simulate the complete dashboard flow"""
    
    print("\n🖥️ SIMULATING COMPLETE DASHBOARD FLOW")
    print("=" * 60)
    
    try:
        # Step 1: Initialize detector
        detector = LLMDamageDetector()
        print("✅ Step 1: LLM Detector initialized")
        
        # Step 2: Get detections (demo mode)
        detections = detector._get_demo_detections()
        print(f"✅ Step 2: Generated {len(detections)} detections")
        
        # Step 3: Filter detections
        filtered_detections = detector.filter_by_confidence(detections)
        print(f"✅ Step 3: Filtered to {len(filtered_detections)} detections")
        
        # Step 4: Convert to class counts
        class_counts = detector.get_part_counts_for_pricing(filtered_detections)
        print(f"✅ Step 4: Generated class counts: {class_counts}")
        
        # Step 5: Get pricing (using compatible user)
        email = "<EMAIL>"
        if class_counts:
            part_prices = get_part_prices(email, class_counts)
            if part_prices:
                total_cost = sum(item['total'] for item in part_prices.values())
                print(f"✅ Step 5: Pricing successful - ₹{total_cost:,}")
                
                # Step 6: Prepare damage info
                damage_info = {}
                for class_id, count in class_counts.items():
                    from app import get_part_name_from_id
                    part_name = get_part_name_from_id(class_id)
                    if part_name:
                        damage_info[part_name] = count
                
                print(f"✅ Step 6: Damage info prepared: {damage_info}")
                
                print(f"\n🎯 DASHBOARD SHOULD SHOW:")
                print(f"   • Damage detected: {len(damage_info)} parts")
                print(f"   • Pricing available: ₹{total_cost:,}")
                print(f"   • No error message")
                
                return True
            else:
                print(f"❌ Step 5: No pricing returned")
                return False
        else:
            print(f"❌ Step 4: No class counts generated")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard simulation failed: {e}")
        return False

def main():
    """Main debugging function"""
    
    print("🚗 DASHBOARD PRICING DEBUG TOOL")
    print("=" * 70)
    
    # Check users
    compatible_users = check_current_users()
    
    # Test LLM detection
    llm_ok = test_llm_detection_flow()
    
    # Test pricing
    pricing_ok = test_pricing_with_compatible_user()
    
    # Simulate complete flow
    flow_ok = simulate_dashboard_flow()
    
    print("\n" + "=" * 70)
    print("🎯 DEBUGGING SUMMARY:")
    print(f"   Compatible Users: {len(compatible_users)} found")
    print(f"   LLM Detection: {'✅ Working' if llm_ok else '❌ Failed'}")
    print(f"   Pricing System: {'✅ Working' if pricing_ok else '❌ Failed'}")
    print(f"   Complete Flow: {'✅ Working' if flow_ok else '❌ Failed'}")
    
    if not compatible_users:
        print("\n⚠️ ISSUE: No compatible users found!")
        print("   SOLUTION: Use demo user (<EMAIL> / demo123)")
    
    if not llm_ok:
        print("\n⚠️ ISSUE: LLM detection not working!")
        print("   SOLUTION: Check API key and network connection")
    
    if not pricing_ok:
        print("\n⚠️ ISSUE: Pricing system not working!")
        print("   SOLUTION: Check database and user vehicle data")
    
    if not flow_ok:
        print("\n⚠️ ISSUE: Complete flow not working!")
        print("   SOLUTION: Check all components above")
    
    if all([compatible_users, llm_ok, pricing_ok, flow_ok]):
        print("\n🎉 ALL SYSTEMS WORKING!")
        print("   If you're still seeing pricing issues:")
        print("   1. Make sure you're logged in as a compatible user")
        print("   2. Try the demo user: <EMAIL> / demo123")
        print("   3. Check the browser console for JavaScript errors")
        print("   4. Clear browser cache and cookies")

if __name__ == "__main__":
    main()
